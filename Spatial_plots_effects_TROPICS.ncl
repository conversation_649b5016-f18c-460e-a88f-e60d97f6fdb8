;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"


begin
  ;--- Read files
  f1 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E_CO2_01_100new1.nc", "r")      ; 1×CO2
  f2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") ; 2×CO2
  f3 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r") ; 2×CO2 + Polar SAG

  ;--- Extract surface temperature variable
  ts_1co2 = f1->TS
  ts_2co2 = f2->TS
  ts_pole = f3->TS
lat = f1->lat
  ;--- Time average [time, lat, lon] → [lat, lon]
  ts_1co2_avg = dim_avg_n_Wrap(ts_1co2, 0)
  ts_2co2_avg = dim_avg_n_Wrap(ts_2co2, 0)
  ts_pole_avg = dim_avg_n_Wrap(ts_pole, 0)

 ts_diff1 = ts_2co2_avg - ts_1co2_avg         ; CO2 warming
ts_diff2 = ts_pole_avg - ts_2co2_avg         ; SAG cooling
ts_diff3 = ts_pole_avg - ts_1co2_avg         ; Net effect of SAG vs baseline
; Calculate global and tropical mean temperature changes using wgt_areaave_Wrap
  rad = 4.0*atan(1.0)/180.0
  wgt = cos(lat * rad)
; Tropical mean (30S-30N)
  lat_indices = ind(lat.ge.-30 .and. lat.le.30)
  ; Global mean (all latitudes)
  ts_diff_global1 = wgt_areaave_Wrap(ts_diff1(lat_indices,:), wgt(lat_indices), 1.0, 0)
  ts_diff_global2 = wgt_areaave_Wrap(ts_diff2(lat_indices,:), wgt(lat_indices), 1.0, 0)
  ts_diff_global3 = wgt_areaave_Wrap(ts_diff3(lat_indices,:), wgt(lat_indices), 1.0, 0)

  ; Tropical mean (30S-30N)
 ; lat_indices = ind(lat.ge.-30 .and. lat.le.30)
 ; ts_diff_tropical_UNIF = wgt_areaave_Wrap(TS_diff_UNIF(lat_indices,:), wgt(lat_indices), 1.0, 0)

  Label_Global_mean1 = "Global Mean="+decimalPlaces(ts_diff_global1,2,True)+ " K"
  ;Label_Tropical_mean_UNIF = "Tropical Mean="+decimalPlaces(ts_diff_tropical_UNIF,2,True)+ " K"
  Label_Global_mean2 = "Global Mean="+decimalPlaces(ts_diff_global2,2,True)+ " K"
  Label_Global_mean3 = "Global Mean="+decimalPlaces(ts_diff_global3,2,True)+ " K"

  print("Global mean temperature difference (2CO2- 1CO2): " + ts_diff_global1 + " K")
  print("Tropical mean temperature difference (SAG- 2CO2): " + ts_diff_global2 + " K")
  print("Tropical mean temperature difference (SAG- 1CO2): " + ts_diff_global3 + " K")

wks = gsn_open_wks("png", "TS_Diff_Plots_Tropical")  ; Or use "png" or "x11"

res= True

  res@gsnDraw             = False
  res@gsnFrame            = False
  res@cnFillOn            = True
  res@cnLinesOn           = False
  res@cnLineLabelsOn      = False
  res@gsnAddCyclic        = True
  res@cnFillMode          = "AreaFill"
  res@gsnLeftString       = ""
  res@gsnRightString      = ""
  res@lbLabelBarOn = False  ; Turn off individual label bars
  res@cnLevelSelectionMode = "ManualLevels"
res@cnMinLevelValF = -3
res@cnMaxLevelValF = 3
res@cnLevelSpacingF = 0.5
 res@mpMaxLatF = 30
  res@mpMinLatF = -30

res@gsnLeftString = "Warming effect (1.4xCO~B~2~N~ - 1xCO~B~2~N~)"
  res@gsnRightString = Label_Global_mean1
res@tmXBLabelFontHeightF    = 0.01    ; Make these labels smaller.
  res@tmYLLabelFontHeightF    = 0.01   ; Make these labels smaller.

plot1 = gsn_csm_contour_map_ce(wks, ts_diff1, res)

;res@gsnCenterString = "SAG effect (Polar~B~SAG~N~ - 1.4xCO~B~2~N~)"
  res@gsnLeftString = "SAG effect (Polar~B~SAG~N~ - 1.4xCO~B~2~N~)"
res@gsnRightString = Label_Global_mean2

plot2 = gsn_csm_contour_map_ce(wks, ts_diff2, res)

res@gsnLeftString = "Combined effect (Polar~B~SAG~N~ -1xCO~B~2~N~)"
  res@gsnRightString = Label_Global_mean3
plot3 = gsn_csm_contour_map_ce(wks, ts_diff3, res)
;--- Panel plots
  pres                  = True
  pres@gsnMaximize      = True
  pres@gsnPanelLabelBar = True

  pres@lbLabelStride = 2
  pres@lbBoxEndCapStyle = "TriangleBothEnds"
  pres@lbOrientation = "Horizontal"  ; Make colorbar horizontal
  pres@pmLabelBarWidthF = 0.6        ; Make colorbar wider (was 0.6)
  pres@pmLabelBarHeightF = 0.07     ; Make colorbar taller (was 0.08)
  pres@lbTitleString = "~F8~D~F~ Surface Temperature (K)"
  pres@lbTitleFont = "times-roman"
  pres@lbTitleFontHeightF = 0.01
  pres@lbTitlePosition = "Bottom"
  ;pres@lbTitleFontHeightF = 0.007    ; Adjust title font size
  pres@lbLabelFontHeightF = 0.01     ; Adjust label font size
pres@gsnPanelYWhiteSpacePercent = 5   ; default ~5–10, reduce for tighter spacing

gsn_panel(wks, (/plot1, plot2, plot3/), (/3,1/), pres)
end