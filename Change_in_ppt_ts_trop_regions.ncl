load "/home/<USER>/Documents/Neethu_IIsc/Programs/India_shapefile/shapefile_utils.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
 
 
 

e_path="/Volumes"

JJA_co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/JJA_Yearly_E_CO2_01_100new1.nc", "r")
DJF_co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/DJF_Yearly_E_CO2_01_100new1.nc", "r")
Ann_co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/Yearly_E_CO2_01_100new1.nc", "r") 

JJA_2co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/JJA_Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r")
DJF_2co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/DJF_Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r")
Ann_2co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") 

JJA_POL=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/JJA_Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r")
DJF_POL=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/DJF_Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r")
Ann_POL=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r") 

print(Ann_co2->lat)

ref=Ann_co2->PRECT(0,:,:)

PREC_Ann_co2=Ann_co2->PRECT(40:99,:,:)*8.64e+7
PREC_JJA_co2=JJA_co2->PRECT(40:99,:,:)*8.64e+7
PREC_DJF_co2=DJF_co2->PRECT(40:99,:,:)*8.64e+7
 
PREC_Ann_2co2=Ann_2co2->PRECT(40:99,:,:)*8.64e+7
PREC_JJA_2co2=JJA_2co2->PRECT(40:99,:,:)*8.64e+7 
PREC_DJF_2co2=DJF_2co2->PRECT(40:99,:,:)*8.64e+7

PREC_Ann_POL=Ann_POL->PRECT(40:99,:,:)*8.64e+7
PREC_JJA_POL=JJA_POL->PRECT(40:99,:,:)*8.64e+7 
PREC_DJF_POL=DJF_POL->PRECT(40:99,:,:)*8.64e+7




P_Annmean_co2=dim_avg_n_Wrap(PREC_Ann_co2,0)
P_JJAmean_co2=dim_avg_n_Wrap(PREC_JJA_co2,0)
P_DJFmean_co2=dim_avg_n_Wrap(PREC_DJF_co2,0)

P_Annmean_2co2=dim_avg_n_Wrap(PREC_Ann_2co2,0)
P_JJAmean_2co2=dim_avg_n_Wrap(PREC_JJA_2co2,0)
P_DJFmean_2co2=dim_avg_n_Wrap(PREC_DJF_2co2,0)

P_Annmean_POL=dim_avg_n_Wrap(PREC_Ann_POL,0)
P_JJAmean_POL=dim_avg_n_Wrap(PREC_JJA_POL,0)
P_DJFmean_POL=dim_avg_n_Wrap(PREC_DJF_POL,0)

copy_VarCoords(ref,P_Annmean_co2)
copy_VarCoords(ref,P_JJAmean_co2)
copy_VarCoords(ref,P_DJFmean_co2)

copy_VarCoords(ref,P_Annmean_2co2)
copy_VarCoords(ref,P_JJAmean_2co2)
copy_VarCoords(ref,P_DJFmean_2co2)

copy_VarCoords(ref,P_Annmean_POL)
copy_VarCoords(ref,P_JJAmean_POL)
copy_VarCoords(ref,P_DJFmean_POL)

P_Annmean_co2@_FillValue=-999.999

change_P_JJAmean_co2=P_JJAmean_co2-P_JJAmean_2co2
change_P_DJFmean_co2=P_DJFmean_co2-P_DJFmean_2co2
change_P_Annmean_co2=P_Annmean_co2-P_Annmean_2co2

change_P_JJAmean_POL=P_JJAmean_POL-P_JJAmean_2co2
change_P_DJFmean_POL=P_DJFmean_POL-P_DJFmean_2co2
change_P_Annmean_POL=P_Annmean_POL-P_Annmean_2co2

copy_VarCoords(ref,change_P_JJAmean_co2)
copy_VarCoords(ref,change_P_Annmean_co2)
copy_VarCoords(ref,change_P_DJFmean_co2)

copy_VarCoords(ref,change_P_JJAmean_POL)
copy_VarCoords(ref,change_P_Annmean_POL)
copy_VarCoords(ref,change_P_DJFmean_POL)

PRECIPITATION_co2=new((/96,144/),typeof(P_Annmean_co2),P_Annmean_co2@_FillValue)

P_NH_co2=change_P_JJAmean_co2({0:90},:)
P_SH_co2=change_P_DJFmean_co2({-90:0},:)
PRECIPITATION_co2(0:47,:)=P_SH_co2
PRECIPITATION_co2(48:95,:)=P_NH_co2

PRECIPITATION_POL=new((/96,144/),typeof(P_Annmean_co2),P_Annmean_co2@_FillValue)

P_NH_POL=change_P_JJAmean_POL({0:90},:)
P_SH_POL=change_P_DJFmean_POL({-90:0},:)
PRECIPITATION_POL(0:47,:)=P_SH_POL
PRECIPITATION_POL(48:95,:)=P_NH_POL

copy_VarCoords(ref,PRECIPITATION_co2)
copy_VarCoords(ref,PRECIPITATION_POL)
;printVarSummary(par)

P_PERCENT_co2=new((/96,144/),typeof(P_Annmean_co2),P_Annmean_co2@_FillValue)

P_NH_PERCENT=((change_P_JJAmean_co2({0:90},:))/P_Annmean_2co2({0:90},:))*100.
P_SH_PERCENT=((change_P_DJFmean_co2({-90:0},:))/P_Annmean_2co2({-90:0},:))*100.
P_PERCENT_co2(0:47,:)=P_SH_PERCENT
P_PERCENT_co2(48:95,:)=P_NH_PERCENT

copy_VarCoords(ref,P_PERCENT_co2)

P_PERCENT_POL=new((/96,144/),typeof(P_Annmean_POL),P_Annmean_POL@_FillValue)

P_NH_PERCENT=((change_P_JJAmean_POL({0:90},:))/P_Annmean_POL({0:90},:))*100.
P_SH_PERCENT=((change_P_DJFmean_POL({-90:0},:))/P_Annmean_POL({-90:0},:))*100.
P_PERCENT_POL(0:47,:)=P_SH_PERCENT
P_PERCENT_POL(48:95,:)=P_NH_PERCENT
;printVarSummary(per)
copy_VarCoords(ref,P_PERCENT_POL)
lat=Ann_co2->lat
lon=Ann_co2->lon
jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
  ;****************************************************
  dummy3=Ann_2co2->PRECC(0:1,1,1)
  dummy3@_FillValue =-999.999
  Global_mean_percent = new(4, typeof(P_Annmean_2co2))  ; Assuming you have 6 data points
Global_mean_percent(0)=(wgt_areaave_Wrap(PRECIPITATION_co2,area,1.0,1)/wgt_areaave_Wrap(P_Annmean_2co2,area,1.0,0))*100.
Global_mean_percent(1)=(wgt_areaave_Wrap(PRECIPITATION_POL,area,1.0,1)/wgt_areaave_Wrap(P_Annmean_2co2,area,1.0,0))*100.

copy_VarCoords(dummy3,Global_mean_percent)

Label_Global_mean_percent = new(2, string)

; Assign values individually (cannot use array slicing)

Label_Global_mean_percent(0) = "Mean=" + decimalPlaces(Global_mean_percent(0), 2, True) + "%"
Label_Global_mean_percent(1) = "Mean=" + decimalPlaces(Global_mean_percent(1), 2, True) + "%"

landsea                    = JJA_co2->LANDFRAC(0,:,:)   
P_PERCENT_LAND_co2             = where(landsea.gt.0,P_PERCENT_co2,P_PERCENT_co2@_FillValue)
P_PERCENT_LAND_POL             = where(landsea.gt.0,P_PERCENT_POL,P_PERCENT_POL@_FillValue)

P_LAND_co2             = where(landsea.gt.0,PRECIPITATION_co2,PRECIPITATION_co2@_FillValue)
P_LAND_POL             = where(landsea.gt.0,PRECIPITATION_POL,PRECIPITATION_POL@_FillValue)

;------------------------------------------------ 
 wks  = gsn_open_wks("eps","plot_precipitation_regions")      ; send graphics to PNG file
;___________________________________________________________________________________________________________________
gsn_define_colormap(wks,"MPL_RdBu") ;

cnres1                             = True
cnres1@gsnMaximize                 = False
cnres1@cnFillPalette = "MPL_RdBu"  ; Temperature colors
cnres1@gsnDraw                     = False
cnres1@gsnFrame                    = False
cnres1@cnLinesOn                   = False
cnres1@cnLineThicknessF            = 0.5
cnres1@cnLineLabelsOn              = False
cnres1@cnFillOn                    = True
cnres1@mpLandFillColor              = "white" ;"cornsilk" ;"black"      ;darkolivegreen ;goldenrod4
cnres1@gsnAddCyclic                 = True

cnres1@mpCenterLonF                  = 0

cnres1@cnLevelSelectionMode         = "ManualLevels"
cnres1@cnMinLevelValF               = -2.0
cnres1@cnMaxLevelValF               = 2.0
cnres1@cnLevelSpacingF              =0.25
cnres1@lbLabelStride                = 4
cnres1@gsnRightStringFontHeightF    = -0.033
cnres1@gsnLeftStringFontHeightF     = -0.033

cnres1@gsnRightString               = ""
cnres1@gsnLeftString                = ""
cnres1@tiMainString                 = "" ;
cnres1@tiMainFont                   = "times-roman"
cnres1@lbLabelBarOn                 = False            ; turn off individual cb's
cnres1@lbBoxEndCapStyle             = "TriangleBothEnds"
cnres1@pmLabelBarWidthF             = 0.04
cnres1@pmLabelBaSSTeightF           = 0.23
cnres1@lbOrientation                = "Vertical"     ; vertical label bar

cnres1@pmTickMarkDisplayMode = "Always"            ; turn on built-in tickmarks
cnres1@lbTitleOn             = True
cnres1@lbLabelStride         = 2
cnres1@lbTitleString         = "P (mm/day)" ;"kJ/cm^2"
cnres1@lbTitleFontHeightF    = 0.022
cnres1@lbTitleFont           = "times-roman"

cnres1@tmXBLabelFontHeightF    = 0.022    ; Make these labels smaller.
cnres1@tmYLLabelFontHeightF    = 0.022    ; Make these labels smaller.


cnres1@tiYAxisFont 	     = "times-roman"
cnres1@tiXAxisFont          = "times-roman"



 cnres1@tmXBMajorLengthF     = 0.01
 cnres1@tmYLMajorLengthF     = 0.01


cnres1@tmYLLabelFont                  = "times-roman"
cnres1@tmXBLabelFont                  = "times-roman"
cnres1@tmXBLabelsOn                   = False

cnres1@tmYRBorderOn = False
cnres1@tmYLBorderOn = False
cnres1@tmXBBorderOn = False
cnres1@tmXTBorderOn = False

cnres1@tmYRLabelsOn = True
cnres1@tmXBMajorLengthF=0.03
cnres1@tmYRMajorLengthF=0.03
cnres1@tmXBOn               = False     ; Turn off top tickmarks
cnres1@tmXTOn               = False     ; Turn off top tickmarks

cnres1@tmXBMajorOutwardLengthF =-0.02
cnres1@tmXBLabelStride =2
cnres1@tmYLLabelStride =2


dlon = 30
dlat = 30

cnres1@mpProjection          = "CylindricalEquidistant"
cnres1@mpPerimOn             =  True
cnres1@mpGridAndLimbOn       =  True
cnres1@mpGridLatSpacingF     =  dlat
cnres1@mpGridLonSpacingF     =  dlon
cnres1@mpGridLineColor       = "gray"
Cases_Label = (/"(a) 1xCO~B1~2 ~NN~"," (b) POLES"/)
;plot_POL = gsn_csm_contour_map(wks, P_LAND_co2, cnres1)
plot = new(2,graphic)
cnres1@gsnLeftString  = Cases_Label(0)
cnres1@gsnRightString = Label_Global_mean_percent(0)
plot(0) = gsn_csm_contour_map(wks, PRECIPITATION_co2, cnres1)

cnres1@gsnLeftString  = Cases_Label(1)
cnres1@gsnRightString = Label_Global_mean_percent(1)
plot(1) = gsn_csm_contour_map(wks, PRECIPITATION_POL, cnres1)
;Define the panel resources for Precipitation
resP_Precip                           = True
resP_Precip@gsnPanelMainString        = ""
resP_Precip@gsnPanelLabelBar          = True
resP_Precip@lbLabelFontHeightF        = 0.015
resP_Precip@lbLabelFont               = "times-roman"
resP_Precip@lbTitleOn                 = True
resP_Precip@lbLabelStride             = 4
resP_Precip@lbTitleString             = "mm ~NN~ day~S~-1"  ; Precipitation unit
resP_Precip@lbTitleFontHeightF        = 0.015
resP_Precip@lbTitleFont               = "times-roman"
resP_Precip@lbTitlePosition           = "Bottom"
resP_Precip@pmLabelBarOrthogonalPosF  = -0.03
resP_Precip@pmLabelBarParallelPosF    = 0.04
resP_Precip@pmLabelBarWidthF          = 0.4
resP_Precip@pmLabelBarHeightF         = 0.05
resP_Precip@gsnPanelYWhiteSpacePercent = 2.5
resP_Precip@gsnPanelXWhiteSpacePercent = 4.5
resP_Precip@gsnMaximize               = True
resP_Precip@gsnPaperOrientation       = "portrait"
gsn_panel(wks, plot(0:1), (/2,2/), resP_Precip)

;colors = [/"red", "blue", "green", "purple", "orange", "brown"/]

gres                  = True
gres@gsnDraw          = False
gres@gsnFrame         = False
gres@gsLineColor       = "Blue"   ; Polyline resources.
gres@gsLineThicknessF  = 6.0      ; thrice thickness
gres@gsFillColor       = "Red"    ; Polygon resources.
gres@gsFillIndex       = -1
;gres@gsFillOpacityF    = 2
gres@gsEdgesOn         = True     ; Line will be drawn around polygon.
gres@gsEdgeColor       = "green"
gres@gsEdgeThicknessF  = 5
gres@gsMarkerIndex     = 16       ; dots   # Polymarker resources.
gres@gsMarkerColor     = "HotPink"
gres@gsMarkerSizeF     = 0.014    ; twice normal size


gres1                  = True
gres1@gsnDraw          = False
gres1@gsnFrame         = False
gres1@gsLineColor       = "Blue"   ; Polyline resources.
gres@gsLineThicknessF  = 6.0      ; thrice thickness
gres1@gsFillColor       = "Red"    ; Polygon resources.
gres1@gsFillIndex       = -1
;gres@gsFillOpacityF    = 2
gres1@gsEdgesOn         = True     ; Line will be drawn around polygon.
gres1@gsEdgeColor       = "red"
gres1@gsEdgeThicknessF  = 3
gres@gsMarkerIndex     = 16       ; dots   # Polymarker resources.
gres1@gsMarkerColor     = "HotPink"
gres1@gsMarkerSizeF     = 0.014    ; twice normal size
  xsquare = (/60,110.0,110.0,60,60/)
  ysquare = (/5,5,35,35,5/)
  Box_SAs = gsn_add_polygon(wks, plot(0), xsquare, ysquare, gres1)
  Box_SAs1 = gsn_add_polygon(wks, plot(1), xsquare, ysquare, gres1)

  xsquare = (/-120,-60.0,-60.0,-120,-120/)
  ysquare = (/0,0,30,30,0/)
  Box_NA = gsn_add_polygon(wks, plot(0), xsquare, ysquare, gres)
  Box_NA1 = gsn_add_polygon(wks, plot(1), xsquare, ysquare, gres)


  xsquare = (/-20,40.0,40.0,-20,-20/)
  ysquare = (/5,5,20,20,5/)
  Box_NAf = gsn_add_polygon(wks, plot(0), xsquare, ysquare, gres1)
  Box_NAf1 = gsn_add_polygon(wks, plot(1), xsquare, ysquare, gres1)

  xsquare = (/-80,-30.0,-30.0,-80,-80/)
  ysquare = (/-5,-5,-35,-35,-5/)
  Box_SA = gsn_add_polygon(wks, plot(0), xsquare, ysquare, gres1)
  Box_SA1 = gsn_add_polygon(wks, plot(1), xsquare, ysquare, gres1)

  xsquare = (/10,50.0,50.0,10,10/)
  ysquare = (/-5,-5,-35,-35,-5/)
  Box_SAf = gsn_add_polygon(wks, plot(0), xsquare, ysquare, gres1)
  Box_SAf1 = gsn_add_polygon(wks, plot(1), xsquare, ysquare, gres1)

  xsquare = (/110,155.0,155.0,110,110/)
  ysquare = (/-5,-5,-25,-25,-5/)
  Box_AUS = gsn_add_polygon(wks, plot(0), xsquare, ysquare, gres1)
  Box_AUS1 = gsn_add_polygon(wks, plot(1), xsquare, ysquare, gres1)

  resPanel = True
  resPanel@gsnPanelBottom = 0.1
  resPanel@gsnPanelTop = 0.9
  resPanelp@gsnPanelMainString        = ""
  resPanel@gsnPanelLabelBar          = True
  resPanel@lbLabelFontHeightF        = 0.015
  resPanel@lbLabelFont               = "times-roman"
  resPanel@lbTitleOn                 = True
  resPanel@lbLabelStride             = 4
  resPanel@lbTitleString             = "mm ~NN~ day~S~-1"  ; Precipitation unit
  resPanel@lbTitleFontHeightF        = 0.015
  resPanel@lbTitleFont               = "times-roman"
  resPanel@lbTitlePosition           = "Bottom"
  resPanel@pmLabelBarOrthogonalPosF  = -0.03
  resPanel@pmLabelBarParallelPosF    = 0.04
  resPanel@pmLabelBarWidthF          = 0.4
  resPanel@pmLabelBarHeightF         = 0.05
  resPanel@gsnPanelYWhiteSpacePercent = 2.5
  resPanel@gsnPanelXWhiteSpacePercent = 4.5
resPanel@gsnMaximize               = True
resPanel@gsnPaperOrientation       = "portrait"
  gsn_panel(wks, plot(0:1), (/1,2/), resPanel)
  
  ;gsn_add_polygon(wks, plot(0), xsquares, ysquares, gres)
 ; gsn_add_polygon(wks, plot(1), xsquares, ysquares, gres)


  exit()
 

endp