#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create histogram for SAG levels (22km, 18km, 16km) 
showing temperature and precipitation changes for Global and Tropics regions.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Data for the three SAG levels
data = {
    'SAG_Level': ['22km', '18km', '16km'],
    'Global_TS_Change': [-3.88, -3.64, -3.31],
    'Global_TS_Error': [0.3, 0.10, 0.10],
    'Global_PPT_Change': [-10.68, -10.33, -10.03],
    'Global_PPT_Error': [0.41, 0.43, 0.35],
    'Tropics_TS_Change': [-3.23, -3.06, -2.73],
    'Tropics_TS_Error': [0.13, 0.12, 0.12],
    'Tropics_PPT_Change': [-8.25, -7.96, -7.86],
    'Tropics_PPT_Error': [0.59, 0.66, 0.59]
}

# Create DataFrame
df = pd.DataFrame(data)

# Set up the figure with subplots
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('SAG Level Analysis: Temperature and Precipitation Changes', fontsize=16, fontweight='bold')

# Color scheme for the three SAG levels
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']  # Blue, Orange, Green
x_pos = np.arange(len(df['SAG_Level']))

# 1. Global Temperature Changes
bars1 = ax1.bar(x_pos, df['Global_TS_Change'], yerr=df['Global_TS_Error'], 
                color=colors, alpha=0.7, capsize=5, edgecolor='black', linewidth=1)
ax1.set_title('Global Temperature Change by SAG Level', fontweight='bold')
ax1.set_xlabel('SAG Level')
ax1.set_ylabel('Temperature Change (K)')
ax1.set_xticks(x_pos)
ax1.set_xticklabels(df['SAG_Level'])
ax1.grid(True, alpha=0.3)
ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add value labels on bars
for i, (bar, val, err) in enumerate(zip(bars1, df['Global_TS_Change'], df['Global_TS_Error'])):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height - 0.1,
             f'{val:.2f}±{err:.2f}K', ha='center', va='top', fontweight='bold')

# 2. Global Precipitation Changes
bars2 = ax2.bar(x_pos, df['Global_PPT_Change'], yerr=df['Global_PPT_Error'], 
                color=colors, alpha=0.7, capsize=5, edgecolor='black', linewidth=1)
ax2.set_title('Global Precipitation Change by SAG Level', fontweight='bold')
ax2.set_xlabel('SAG Level')
ax2.set_ylabel('Precipitation Change (%)')
ax2.set_xticks(x_pos)
ax2.set_xticklabels(df['SAG_Level'])
ax2.grid(True, alpha=0.3)
ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add value labels on bars
for i, (bar, val, err) in enumerate(zip(bars2, df['Global_PPT_Change'], df['Global_PPT_Error'])):
    height = bar.get_height()
    ax2.text(bar.get_x() + bar.get_width()/2., height - 0.3,
             f'{val:.2f}±{err:.2f}%', ha='center', va='top', fontweight='bold')

# 3. Tropics Temperature Changes
bars3 = ax3.bar(x_pos, df['Tropics_TS_Change'], yerr=df['Tropics_TS_Error'], 
                color=colors, alpha=0.7, capsize=5, edgecolor='black', linewidth=1)
ax3.set_title('Tropics (30°N-30°S) Temperature Change by SAG Level', fontweight='bold')
ax3.set_xlabel('SAG Level')
ax3.set_ylabel('Temperature Change (K)')
ax3.set_xticks(x_pos)
ax3.set_xticklabels(df['SAG_Level'])
ax3.grid(True, alpha=0.3)
ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add value labels on bars
for i, (bar, val, err) in enumerate(zip(bars3, df['Tropics_TS_Change'], df['Tropics_TS_Error'])):
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height - 0.1,
             f'{val:.2f}±{err:.2f}K', ha='center', va='top', fontweight='bold')

# 4. Tropics Precipitation Changes
bars4 = ax4.bar(x_pos, df['Tropics_PPT_Change'], yerr=df['Tropics_PPT_Error'], 
                color=colors, alpha=0.7, capsize=5, edgecolor='black', linewidth=1)
ax4.set_title('Tropics (30°N-30°S) Precipitation Change by SAG Level', fontweight='bold')
ax4.set_xlabel('SAG Level')
ax4.set_ylabel('Precipitation Change (%)')
ax4.set_xticks(x_pos)
ax4.set_xticklabels(df['SAG_Level'])
ax4.grid(True, alpha=0.3)
ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# Add value labels on bars
for i, (bar, val, err) in enumerate(zip(bars4, df['Tropics_PPT_Change'], df['Tropics_PPT_Error'])):
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height - 0.2,
             f'{val:.2f}±{err:.2f}%', ha='center', va='top', fontweight='bold')

# Adjust layout to prevent overlap
plt.tight_layout()

# Save the figure
plt.savefig('sag_histogram_analysis.png', dpi=300, bbox_inches='tight')
plt.savefig('sag_histogram_analysis.pdf', bbox_inches='tight')

# Show the plot
plt.show()

# Print summary statistics
print("\n" + "="*60)
print("SAG LEVEL ANALYSIS SUMMARY")
print("="*60)
print(f"{'SAG Level':<10} {'Global TS (K)':<15} {'Global PPT (%)':<15} {'Tropics TS (K)':<15} {'Tropics PPT (%)':<15}")
print("-"*75)
for i, row in df.iterrows():
    print(f"{row['SAG_Level']:<10} "
          f"{row['Global_TS_Change']:>6.2f}±{row['Global_TS_Error']:.2f}    "
          f"{row['Global_PPT_Change']:>7.2f}±{row['Global_PPT_Error']:.2f}     "
          f"{row['Tropics_TS_Change']:>6.2f}±{row['Tropics_TS_Error']:.2f}     "
          f"{row['Tropics_PPT_Change']:>6.2f}±{row['Tropics_PPT_Error']:.2f}")

print("\nNotes:")
print("- TS: Temperature Surface change")
print("- PPT: Precipitation change")
print("- Tropics region: 30°N to 30°S")
print("- Error bars represent ± standard error")
print("- Figures saved as 'sag_histogram_analysis.png' and 'sag_histogram_analysis.pdf'")
