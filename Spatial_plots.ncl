;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"


begin
  ;--- Read files
  f1 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E_CO2_01_100new1.nc", "r")      ; 1×CO2
  f2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") ; 2×CO2
  f3 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r") ; 2×CO2 + Polar SAG

  ;--- Extract surface temperature variable
  ts_1co2 = f1->TS
  ts_2co2 = f2->TS
  ts_pole = f3->TS

  ;--- Time average [time, lat, lon] → [lat, lon]
  ts_1co2_avg = dim_avg_n_Wrap(ts_1co2, 0)
  ts_2co2_avg = dim_avg_n_Wrap(ts_2co2, 0)
  ts_pole_avg = dim_avg_n_Wrap(ts_pole, 0)

  ;--- Open workstation
  wks = gsn_open_wks("png", "TS_spatial_panels")   ; Output: TS_spatial_panels.png

  ;--- Define color map and plotting options
  gsn_define_colormap(wks, "BlueWhiteOrangeRed")

  res                     = True
  res@gsnDraw             = False
  res@gsnFrame            = False
  res@cnFillOn            = True
  res@cnLinesOn           = False
  res@cnLineLabelsOn      = False
  res@gsnAddCyclic        = True
  res@cnFillMode          = "RasterFill"
  res@gsnLeftString       = ""
  res@gsnRightString      = ""

  ;--- Common color bounds for comparison (adjust as needed)
  res@cnMinLevelValF      = 288
  res@cnMaxLevelValF      = 294
  res@cnLevelSpacingF     = 2
  res@lbLabelBarOn = False  ; Turn off individual label bars
  res@tiMainFontHeightF   = 0.015

  ;--- Titles
  res@gsnCenterString     = "1xCO~B~2"
  plot1 = gsn_csm_contour_map_ce(wks, ts_1co2_avg, res)

  res@gsnCenterString     = "2xCO~B~2"
  plot2 = gsn_csm_contour_map_ce(wks, ts_2co2_avg, res)

  res@gsnCenterString     = "2xCO~B~2~N~ + Polar~B~SAG~"
  plot3 = gsn_csm_contour_map_ce(wks, ts_pole_avg, res)

  ;--- Panel plots
  pres                  = True
  pres@gsnMaximize      = True
  pres@gsnPanelLabelBar = True
  pres@lbLabelFontHeightF = 0.012

  gsn_panel(wks, (/plot1, plot2, plot3/), (/2,2/), pres)

end
