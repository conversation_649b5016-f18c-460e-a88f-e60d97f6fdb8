#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to show SAG altitude sensitivity relative to 1CO2 baseline
by subtracting the 1CO2-2CO2 changes from the SAG data.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Original SAG data (relative to 2CO2)
sag_data = {
    'SAG_Level': ['22km', '18km', '16km'],
    'SAG_Altitude': [22, 18, 16],  # For numerical plotting
    'Global_TS_Change_2CO2': [-3.88, -3.64, -3.31],
    'Global_TS_Error_2CO2': [0.3, 0.10, 0.10],
    'Global_PPT_Change_2CO2': [-10.68, -10.33, -10.03],
    'Global_PPT_Error_2CO2': [0.41, 0.43, 0.35],
    'Tropics_TS_Change_2CO2': [-3.23, -3.06, -2.73],
    'Tropics_TS_Error_2CO2': [0.13, 0.12, 0.12],
    'Tropics_PPT_Change_2CO2': [-8.25, -7.96, -7.86],
    'Tropics_PPT_Error_2CO2': [0.59, 0.66, 0.59]
}

# 1CO2-2CO2 baseline values (to subtract from SAG data)
baseline_1co2_2co2 = {
    'Global_TS_Change': -3.77,  # K
    'Global_TS_Error': 0.18,
    'Global_PPT_Change': -7.39,  # %
    'Global_PPT_Error': 0.49,
    'Tropics_TS_Change': -2.96,  # K
    'Tropics_TS_Error': 0.18,
    'Tropics_PPT_Change': -4.53,  # %
    'Tropics_PPT_Error': 0.64
}

df = pd.DataFrame(sag_data)

# Calculate SAG effects relative to 1CO2 by subtracting 1CO2-2CO2 baseline
df['Global_TS_Change_1CO2'] = df['Global_TS_Change_2CO2'] - baseline_1co2_2co2['Global_TS_Change']
df['Global_PPT_Change_1CO2'] = df['Global_PPT_Change_2CO2'] - baseline_1co2_2co2['Global_PPT_Change']
df['Tropics_TS_Change_1CO2'] = df['Tropics_TS_Change_2CO2'] - baseline_1co2_2co2['Tropics_TS_Change']
df['Tropics_PPT_Change_1CO2'] = df['Tropics_PPT_Change_2CO2'] - baseline_1co2_2co2['Tropics_PPT_Change']

# Error propagation: σ(a-b) = sqrt(σa² + σb²)
df['Global_TS_Error_1CO2'] = np.sqrt(df['Global_TS_Error_2CO2']**2 + baseline_1co2_2co2['Global_TS_Error']**2)
df['Global_PPT_Error_1CO2'] = np.sqrt(df['Global_PPT_Error_2CO2']**2 + baseline_1co2_2co2['Global_PPT_Error']**2)
df['Tropics_TS_Error_1CO2'] = np.sqrt(df['Tropics_TS_Error_2CO2']**2 + baseline_1co2_2co2['Tropics_TS_Error']**2)
df['Tropics_PPT_Error_1CO2'] = np.sqrt(df['Tropics_PPT_Error_2CO2']**2 + baseline_1co2_2co2['Tropics_PPT_Error']**2)

# Create figure with dual y-axes
fig, ax1 = plt.subplots(figsize=(12, 8))

# Plot temperature on left y-axis
color_temp = 'tab:red'
ax1.set_xlabel('SAG Level (km)', fontsize=12, fontweight='bold')
ax1.set_ylabel('Temperature Change relative to 1CO2 (K)', color=color_temp, fontsize=12, fontweight='bold')

# Global temperature
line1 = ax1.errorbar(df['SAG_Altitude'], df['Global_TS_Change_1CO2'], 
                     yerr=df['Global_TS_Error_1CO2'], 
                     color=color_temp, marker='o', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Global Temperature', linestyle='-')

# Tropics temperature  
line2 = ax1.errorbar(df['SAG_Altitude'], df['Tropics_TS_Change_1CO2'], 
                     yerr=df['Tropics_TS_Error_1CO2'], 
                     color='darkred', marker='s', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Tropics Temperature', linestyle='--')

ax1.tick_params(axis='y', labelcolor=color_temp)
ax1.grid(True, alpha=0.3)
ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)

# Create second y-axis for precipitation
ax2 = ax1.twinx()
color_ppt = 'tab:blue'
ax2.set_ylabel('Precipitation Change relative to 1CO2 (%)', color=color_ppt, fontsize=12, fontweight='bold')

# Global precipitation
line3 = ax2.errorbar(df['SAG_Altitude'], df['Global_PPT_Change_1CO2'], 
                     yerr=df['Global_PPT_Error_1CO2'], 
                     color=color_ppt, marker='^', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Global Precipitation', linestyle='-')

# Tropics precipitation
line4 = ax2.errorbar(df['SAG_Altitude'], df['Tropics_PPT_Change_1CO2'], 
                     yerr=df['Tropics_PPT_Error_1CO2'], 
                     color='darkblue', marker='D', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Tropics Precipitation', linestyle='--')

ax2.tick_params(axis='y', labelcolor=color_ppt)
ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)

# Customize x-axis
ax1.set_xticks(df['SAG_Altitude'])
ax1.set_xticklabels(df['SAG_Level'])
ax1.invert_xaxis()  # Higher resolution (lower km) on the right

# Add title and legend
#plt.title('Impact of injection altitude on surface temperature and precipitation responses\n(Relative to 1CO2 baseline)', 
 #         fontsize=14, fontweight='bold', pad=20)

# Combine legends from both axes - positioned below plot horizontally
lines1, labels1 = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()

#ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=2)
ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=4)

# Add text annotations to highlight the key finding
#ax1.text(0.98, 0.02, 'Additional effects beyond CO2 doubling\nTemperature: Strong altitude dependence\nPrecipitation: Weak altitude dependence', 
 #        transform=ax1.transAxes, fontsize=11, fontweight='bold',
  #       verticalalignment='bottom', horizontalalignment='right',
   #      bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

plt.tight_layout()

# Save the figure
plt.savefig('altitude_sensitivity_relative_1co2_1.png', dpi=300, bbox_inches='tight')
plt.savefig('altitude_sensitivity_relative_1co2_1.pdf', bbox_inches='tight')

# Print detailed analysis
print("\n" + "="*80)
print("SAG ALTITUDE SENSITIVITY ANALYSIS - RELATIVE TO 1CO2 BASELINE")
print("="*80)

print("\nOriginal SAG data (relative to 2CO2):")
print(f"{'SAG Level':<10} {'Global TS':<12} {'Global PPT':<13} {'Tropics TS':<12} {'Tropics PPT':<13}")
print("-"*65)
for i, row in df.iterrows():
    print(f"{row['SAG_Level']:<10} "
          f"{row['Global_TS_Change_2CO2']:>6.2f}±{row['Global_TS_Error_2CO2']:.2f}K  "
          f"{row['Global_PPT_Change_2CO2']:>7.2f}±{row['Global_PPT_Error_2CO2']:.2f}%  "
          f"{row['Tropics_TS_Change_2CO2']:>6.2f}±{row['Tropics_TS_Error_2CO2']:.2f}K  "
          f"{row['Tropics_PPT_Change_2CO2']:>6.2f}±{row['Tropics_PPT_Error_2CO2']:.2f}%")

print(f"\n1CO2-2CO2 baseline values:")
print(f"Global:  TS = {baseline_1co2_2co2['Global_TS_Change']:.2f}±{baseline_1co2_2co2['Global_TS_Error']:.2f}K, "
      f"PPT = {baseline_1co2_2co2['Global_PPT_Change']:.2f}±{baseline_1co2_2co2['Global_PPT_Error']:.2f}%")
print(f"Tropics: TS = {baseline_1co2_2co2['Tropics_TS_Change']:.2f}±{baseline_1co2_2co2['Tropics_TS_Error']:.2f}K, "
      f"PPT = {baseline_1co2_2co2['Tropics_PPT_Change']:.2f}±{baseline_1co2_2co2['Tropics_PPT_Error']:.2f}%")

print("\nSAG effects relative to 1CO2 (SAG - 1CO2-2CO2 baseline):")
print(f"{'SAG Level':<10} {'Global TS':<12} {'Global PPT':<13} {'Tropics TS':<12} {'Tropics PPT':<13}")
print("-"*65)
for i, row in df.iterrows():
    print(f"{row['SAG_Level']:<10} "
          f"{row['Global_TS_Change_1CO2']:>6.2f}±{row['Global_TS_Error_1CO2']:.2f}K  "
          f"{row['Global_PPT_Change_1CO2']:>7.2f}±{row['Global_PPT_Error_1CO2']:.2f}%  "
          f"{row['Tropics_TS_Change_1CO2']:>6.2f}±{row['Tropics_TS_Error_1CO2']:.2f}K  "
          f"{row['Tropics_PPT_Change_1CO2']:>6.2f}±{row['Tropics_PPT_Error_1CO2']:.2f}%")

# Calculate sensitivity metrics for 1CO2-relative data
global_temp_cv = np.std(np.abs(df['Global_TS_Change_1CO2'])) / np.mean(np.abs(df['Global_TS_Change_1CO2'])) * 100
global_ppt_cv = np.std(np.abs(df['Global_PPT_Change_1CO2'])) / np.mean(np.abs(df['Global_PPT_Change_1CO2'])) * 100
tropics_temp_cv = np.std(np.abs(df['Tropics_TS_Change_1CO2'])) / np.mean(np.abs(df['Tropics_TS_Change_1CO2'])) * 100
tropics_ppt_cv = np.std(np.abs(df['Tropics_PPT_Change_1CO2'])) / np.mean(np.abs(df['Tropics_PPT_Change_1CO2'])) * 100

print(f"\nAltitude Sensitivity (Coefficient of Variation - relative to 1CO2):")
print(f"  Global Temperature:     {global_temp_cv:.1f}%")
print(f"  Global Precipitation:   {global_ppt_cv:.1f}%")
print(f"  Tropics Temperature:    {tropics_temp_cv:.1f}%")
print(f"  Tropics Precipitation:  {tropics_ppt_cv:.1f}%")

print(f"\nConclusion (relative to 1CO2):")
print(f"Temperature shows {global_temp_cv/global_ppt_cv:.1f}x more sensitivity to altitude than precipitation (Global)")
print(f"Temperature shows {tropics_temp_cv/tropics_ppt_cv:.1f}x more sensitivity to altitude than precipitation (Tropics)")

plt.show()
