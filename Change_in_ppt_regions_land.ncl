load "/home/<USER>/Documents/Neethu_IIsc/Programs/India_shapefile/shapefile_utils.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
 
 
 

e_path="/Volumes"

JJA_co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/JJA_Yearly_E_CO2_01_100new1.nc", "r")
DJF_co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/DJF_Yearly_E_CO2_01_100new1.nc", "r")
Ann_co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/Yearly_E_CO2_01_100new1.nc", "r") 

JJA_2co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/JJA_Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r")
DJF_2co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/DJF_Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r")
Ann_2co2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") 

JJA_POL=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/JJA_Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r")
DJF_POL=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/DJF_Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r")
Ann_POL=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/25_76Mt_560/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r") 

print(Ann_co2->lat)

ref=Ann_co2->PRECT(0,:,:)

PREC_Ann_co2=Ann_co2->PRECT(40:99,:,:)*8.64e+7
PREC_JJA_co2=JJA_co2->PRECT(40:99,:,:)*8.64e+7
PREC_DJF_co2=DJF_co2->PRECT(40:99,:,:)*8.64e+7
 
PREC_Ann_2co2=Ann_2co2->PRECT(40:99,:,:)*8.64e+7
PREC_JJA_2co2=JJA_2co2->PRECT(40:99,:,:)*8.64e+7 
PREC_DJF_2co2=DJF_2co2->PRECT(40:99,:,:)*8.64e+7

PREC_Ann_POL=Ann_POL->PRECT(40:99,:,:)*8.64e+7
PREC_JJA_POL=JJA_POL->PRECT(40:99,:,:)*8.64e+7 
PREC_DJF_POL=DJF_POL->PRECT(40:99,:,:)*8.64e+7




P_Annmean_co2=dim_avg_n_Wrap(PREC_Ann_co2,0)
P_JJAmean_co2=dim_avg_n_Wrap(PREC_JJA_co2,0)
P_DJFmean_co2=dim_avg_n_Wrap(PREC_DJF_co2,0)

P_Annmean_2co2=dim_avg_n_Wrap(PREC_Ann_2co2,0)
P_JJAmean_2co2=dim_avg_n_Wrap(PREC_JJA_2co2,0)
P_DJFmean_2co2=dim_avg_n_Wrap(PREC_DJF_2co2,0)

P_Annmean_POL=dim_avg_n_Wrap(PREC_Ann_POL,0)
P_JJAmean_POL=dim_avg_n_Wrap(PREC_JJA_POL,0)
P_DJFmean_POL=dim_avg_n_Wrap(PREC_DJF_POL,0)

copy_VarCoords(ref,P_Annmean_co2)
copy_VarCoords(ref,P_JJAmean_co2)
copy_VarCoords(ref,P_DJFmean_co2)

copy_VarCoords(ref,P_Annmean_2co2)
copy_VarCoords(ref,P_JJAmean_2co2)
copy_VarCoords(ref,P_DJFmean_2co2)

copy_VarCoords(ref,P_Annmean_POL)
copy_VarCoords(ref,P_JJAmean_POL)
copy_VarCoords(ref,P_DJFmean_POL)

P_Annmean_co2@_FillValue=-999.999

change_P_JJAmean_co2=P_JJAmean_co2-P_JJAmean_2co2
change_P_DJFmean_co2=P_DJFmean_co2-P_DJFmean_2co2
change_P_Annmean_co2=P_Annmean_co2-P_Annmean_2co2

change_P_JJAmean_POL=P_JJAmean_POL-P_JJAmean_2co2
change_P_DJFmean_POL=P_DJFmean_POL-P_DJFmean_2co2
change_P_Annmean_POL=P_Annmean_POL-P_Annmean_2co2

copy_VarCoords(ref,change_P_JJAmean_co2)
copy_VarCoords(ref,change_P_Annmean_co2)
copy_VarCoords(ref,change_P_DJFmean_co2)

copy_VarCoords(ref,change_P_JJAmean_POL)
copy_VarCoords(ref,change_P_Annmean_POL)
copy_VarCoords(ref,change_P_DJFmean_POL)


undf=-999.999
PT=new((/9,60,96,144/),"float",undf)
PT(0,:,:,:)=PREC_Ann_2co2
PT(1,:,:,:)=PREC_JJA_2co2
PT(2,:,:,:)=PREC_DJF_2co2
PT(3,:,:,:)=PREC_Ann_co2
PT(4,:,:,:)=PREC_JJA_co2
PT(5,:,:,:)=PREC_DJF_co2
PT(6,:,:,:)=PREC_Ann_POL
PT(7,:,:,:)=PREC_JJA_POL
PT(8,:,:,:)=PREC_DJF_POL

landsea=new((/9,60,96,144/),"float",undf)
landsea(0:8,:,:,:)         = Ann_co2->LANDFRAC(40:99,:,:)   
printVarSummary(landsea)
dummy=Ann_co2->PRECT(40:99,:,:);new((/9,60,96,144/),"float",undf)
PT_masked=new((/9,60,96,144/),typeof(dummy),undf)
PT_masked= where(landsea.gt.0,PT,undf)
copy_VarCoords(dummy, PT_masked)
printVarSummary(PT_masked)
; North African monsoon-------------------------------------------------NAf
NAf_PT_masked_2co2=dim_avg_n_Wrap(PT_masked(1,:,{5:20},{-20:40}),(/1,2/)) 
NAf_PT_masked_co2=dim_avg_n_Wrap(PT_masked(4,:,{5:20},{-20:40}),(/1,2/)) 
NAf_PT_masked_POL=dim_avg_n_Wrap(PT_masked(7,:,{5:20},{-20:40}),(/1,2/)) 

; North American monsoon-------------------------------------------------NA
NA_PT_masked_2co2=dim_avg_n_Wrap(PT_masked(1,:,{0:30},{240:300}),(/1,2/))
NA_PT_masked_co2=dim_avg_n_Wrap(PT_masked(4,:,{0:30},{240:300}),(/1,2/))
NA_PT_masked_POL=dim_avg_n_Wrap(PT_masked(7,:,{0:30},{240:300}),(/1,2/))

;South Asian monsoon-------------------------------------------------SAs
SAs_PT_masked_2co2=dim_avg_n_Wrap(PT_masked(1,:,{5:35},{60:110}),(/1,2/)) 
SAs_PT_masked_co2=dim_avg_n_Wrap(PT_masked(4,:,{5:35},{60:110}),(/1,2/)) 
SAs_PT_masked_POL=dim_avg_n_Wrap(PT_masked(7,:,{5:35},{60:110}),(/1,2/)) 

;NHMI_masked(i,:)=((NAf_PT_masked(i,:)+NA_PT_masked(i,:)+SAs_PT_masked(i,:)))/3

asciiwrite ("NAf_PT_masked_2co2.txt" ,  NAf_PT_masked_2co2)
asciiwrite ("NAf_PT_masked_co2.txt" ,  NAf_PT_masked_co2)
asciiwrite ("NAf_PT_masked_POL.txt" ,  NAf_PT_masked_POL)

asciiwrite ("NA_PT_masked_2co2.txt" ,  NA_PT_masked_2co2)
asciiwrite ("NA_PT_masked_co2.txt" ,  NA_PT_masked_co2)
asciiwrite ("NA_PT_masked_POL.txt" ,  NA_PT_masked_POL)

asciiwrite ("SAs_PT_masked_2co2.txt" ,  SAs_PT_masked_2co2)
asciiwrite ("SAs_PT_masked_co2.txt" ,  SAs_PT_masked_co2)
asciiwrite ("SAs_PT_masked_POL.txt" ,  SAs_PT_masked_POL)

end