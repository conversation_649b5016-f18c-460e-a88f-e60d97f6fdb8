;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
 f1 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F_CO2_01_60_new1.nc", "r")      ; 1×CO2
  f2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F2000_2CO2_01_60_560_2D_1.nc", "r") ; 2×CO2
  f3= addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F2000_65_90_560_01_60_2D_1.nc", "r") ; 2×CO2 + Polar SAG



  ; Read in variables (assumed to be time,lat,lon)
  ts1  = f1->TS(30:59,:,:)
  fsnt1 = f1->FSNT(30:59,:,:)
  flnt1 = f1->FLNT(30:59,:,:)
  ts2  = f2->TS(30:59,:,:)
  fsnt2 = f2->FSNT(30:59,:,:)
  flnt2 = f2->FLNT(30:59,:,:)

  ts3  = f3->TS(30:59,:,:)
  fsnt3 = f3->FSNT(30:59,:,:)
  flnt3 = f3->FLNT(30:59,:,:)

  ; Calculate net TOA radiation
  net1 = fsnt1 - flnt1
  net2 = fsnt2 - flnt2
  net3 = fsnt3 - flnt3

  lat  = f1->lat
  lon  = f1->lon
  nlat = dimsizes(lat)
  nlon = dimsizes(lon)
  ; Function to compute spatial ERF
  function calc_erf(ts, net)
  local erf, i, j, reg, nlat, nlon, missing_val
  begin
    nlat = dimsizes(ts(0,:,0))
    nlon = dimsizes(ts(0,0,:))
    missing_val = 9.96921e+36

    erf = new((/nlat, nlon/), typeof(ts), missing_val)

    do i = 0, nlat - 1
      do j = 0, nlon - 1
        if (all(.not.ismissing(ts(:,i,j))) .and. all(.not.ismissing(net(:,i,j)))) then
          reg = regline(ts(:,i,j), net(:,i,j))
          erf(i,j) = reg@yintercept
        else
          erf(i,j) = missing_val
        end if
      end do
    end do

    return(erf)
  end
;end


  erf1 = calc_erf(ts1, net1)
  erf2 = calc_erf(ts2, net2)
  erf3 = calc_erf(ts3, net3)

  ; Compute 3 effects
  erf_warming  = erf2 - erf1
  erf_sag      = erf3 - erf2
  erf_combined = erf3 - erf1

  ; ========== Plotting ==========
  wks = gsn_open_wks("pdf","ERF_Panel")


  res                     = True
  res@gsnDraw             = False
  res@gsnFrame            = False
  res@cnFillOn            = True
  res@cnLinesOn           = False
  res@cnFillMode         = "AreaFill"
  ;res@cnFillPalette       = "vik"
  res@gsnAddCyclic        = True
  res@mpCenterLonF        = 0
  res@tiMainFontHeightF   = 0.015
  res@gsnRightString      = "ERF (W/m²)"
  res@lbLabelBarOn = False  ; Turn off individual label bars

  res@cnLevelSelectionMode = "ManualLevels"
  res@cnMinLevelValF       = -8.0
  res@cnMaxLevelValF       = 4.0
  res@cnLevelSpacingF      = 2

  plot1 = gsn_csm_contour_map(wks, erf_warming, res)
  plot2 = gsn_csm_contour_map(wks, erf_sag, res)
  plot3 = gsn_csm_contour_map(wks, erf_combined, res)

  ; Add titles
  res@tiMainString = "Warming Effect (1.4xCO₂ - 1xCO₂)"
  plot1@gsnPanelFigureStrings = "Warming Effect"

  res@tiMainString = "SAG Effect (SAG - 1.4xCO₂)"
  plot2@gsnPanelFigureStrings = "SAG Effect"

  res@tiMainString = "Combined Effect (SAG - 1xCO₂)"
  plot3@gsnPanelFigureStrings = "Combined Effect"

  ; Panel plot
pres = True
pres@gsnMaximize         = True
pres@gsnPanelLabelBar    = True
pres@lbLabelFontHeightF  = 0.012
pres@gsnPanelMainString  = "Spatial Distribution of Effective Radiative Forcing"
pres@gsnPanelFigureStrings = (/"Warming Effect", "SAG Effect", "Combined Effect"/)

  gsn_panel(wks, (/plot1, plot2, plot3/), (/1,3/), pres)

end
