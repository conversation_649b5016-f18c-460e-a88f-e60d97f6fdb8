;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
    ;--- Read files
  e_1CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E_CO2_01_100new1.nc", "r")
  e_2CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r")
  e_POL  = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r")
  ;--- Read TS and coordinates
  ts_1CO2 = e_1CO2->TS(:,:,:)
  ts_2CO2 = e_2CO2->TS(:,:,:)
  ts_POL  = e_POL->TS(:,:,:)
  lat     = e_1CO2->lat
  ;--- Zonal mean (longitude average), then time mean, final shape: [lat]
  ts_1CO2_zm1 = dim_avg_n_Wrap(ts_1CO2, 2)      ; [time, lat]
  ts_1CO2_zm = dim_avg_n_Wrap(ts_1CO2_zm1, 0)   ; [lat]

  ts_2CO2_zm1 = dim_avg_n_Wrap(ts_2CO2, 2)
  ts_2CO2_zm = dim_avg_n_Wrap(ts_2CO2_zm1, 0)

  ts_POL_zm1  = dim_avg_n_Wrap(ts_POL, 2)
  ts_POL_zm  = dim_avg_n_Wrap(ts_POL_zm1, 0)
  lat        = e_1CO2->lat
printVarSummary(ts_POL_zm)
  ;--- Open workstation
 ;wks = gsn_open_wks("png", "Lat_vs_SurfaceTemp_withLegend")
 ; wks = gsn_open_wks("pdf", "Lat_vs_SurfaceTemp_withLegend")

  ;--- Plot resources
  res                          = True
  res@gsnDraw                  = False
  res@gsnFrame                 = False
  res@xyLineColors             = (/"blue","red","green"/)
  res@xyDashPattern            = (/0, 0, 0/)
  res@xyLineThicknesses        = (/2.0,2.0,2.0/)
  res@tiMainString             = "Latitude vs Surface Temperature"
  res@tiXAxisString            = "Latitude (~F34~0~F~N)"
  res@tiYAxisString            = "Surface Temperature (K)"

    res@lgLabelFontHeightF = 0.015
  res@xyExplicitLegendLabels = (/"1xCO~B~2","2xCO~B~2","2xCO~B~2~N~ + Polar~B~SAG~"/)
  res@pmLegendDisplayMode = "Always"
  res@pmLegendSide = "Top"
  res@pmLegendParallelPosF = 0.788
  res@pmLegendOrthogonalPosF= -0.28
  res@pmLegendWidthF = 0.15
  res@pmLegendHeightF = 0.10
  res@lgPerimOn = True
  ;--- Create main plot
  plot = gsn_csm_xy(wks, lat, (/ts_1CO2_zm, ts_2CO2_zm, ts_POL_zm/), res)
  draw(plot)
  frame(wks)
end