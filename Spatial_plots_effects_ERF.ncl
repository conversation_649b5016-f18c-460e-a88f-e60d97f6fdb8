;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"


begin
  ;--- Read files
  e_1CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E_CO2_01_100new1.nc", "r")      ; 1×CO2
  e_1_4CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") ; 2×CO2
  e_SAG = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r") ; 2×CO2 + Polar SAG

  f_1CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F_CO2_01_60_new1.nc", "r")      ; 1×CO2
  f_1_4CO2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F2000_2CO2_01_60_560_2D_1.nc", "r") ; 2×CO2
  f_SAG = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F2000_65_90_560_01_60_2D_1.nc", "r") ; 2×CO2 + Polar SAG

NET_RAD_SST_1CO2          = (/f_1CO2->FSNT(30:59,:,:) - f_1CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_1_4CO2          = (/f_1_4CO2->FSNT(30:59,:,:) - f_1_4CO2->FLNT(30:59,:,:)/)
NET_RAD_SST_SAG     = (/f_SAG->FSNT(30:59,:,:) - f_SAG->FLNT(30:59,:,:)/)


NET_RAD_SOM_1CO2          = (/e_1CO2->FSNT(40:99,:,:) - e_1CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_1_4CO2          = (/e_1_4CO2->FSNT(40:99,:,:) - e_1_4CO2->FLNT(40:99,:,:)/)
NET_RAD_SOM_SAG     = (/e_SAG->FSNT(40:99,:,:) - e_SAG->FLNT(40:99,:,:)/)

;---------------------------------------------------------
dummy1=f_1CO2->TS(:,:,:)
dummy2=e_1CO2->TS(:,:,:)

copy_VarCoords(dummy1,NET_RAD_SST_1CO2)
copy_VarCoords(dummy1,NET_RAD_SST_1_4CO2)
copy_VarCoords(dummy1,NET_RAD_SST_SAG)



copy_VarCoords(dummy2,NET_RAD_SOM_1CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_1_4CO2)
copy_VarCoords(dummy2,NET_RAD_SOM_SAG)

;******************************************                 Areal average
lat=f_1CO2->lat
lon=f_1CO2->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************   
dummy3=f_1CO2->lat
copy_VarCoords(dummy3,area)
;print(area)
dummy4=e_1CO2->TS(0,:,:)


; Average over time and longitude to get zonal mean forcing
delta_N_SST_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1_4CO2, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1CO2, 2), 0)

delta_N_SST_SAG_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_SAG, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1_4CO2, 2), 0)
delta_N_SST_SAG_lat_comb = dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_SAG, 2), 0) - dim_avg_n_Wrap(dim_avg_n_Wrap(NET_RAD_SST_1CO2, 2), 0)

DELTA_T_SST_1CO2_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_1_4CO2->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:),2),0)
DELTA_T_SST_SAG_lat = dim_avg_n_Wrap(dim_avg_n_Wrap(f_SAG->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_1_4CO2->TS(30:59,:,:),2),0)
DELTA_T_SST_SAG_lat_comb = dim_avg_n_Wrap(dim_avg_n_Wrap(f_SAG->TS(30:59,:,:),2),0) - dim_avg_n_Wrap(dim_avg_n_Wrap(f_1CO2->TS(30:59,:,:),2),0)


printVarSummary(DELTA_T_SST_1CO2_lat)
DELTA_N_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_1_4CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))
DELTA_N_SST_SAG_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SST_SAG,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1_4CO2,area,1.0,1))
DELTA_N_SST_SAG_GLOBALmean_comb = (wgt_areaave_Wrap(NET_RAD_SST_SAG,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SST_1CO2,area,1.0,1))

DELTA_T_SST_1CO2_GLOBALmean = (wgt_areaave_Wrap(f_1_4CO2->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_SAG_GLOBALmean = (wgt_areaave_Wrap(f_SAG->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1_4CO2->TS(30:59,:,:),area,1.0,1))
DELTA_T_SST_SAG_GLOBALmean_comb = (wgt_areaave_Wrap(f_SAG->TS(30:59,:,:),area,1.0,1)) - (wgt_areaave_Wrap(f_1CO2->TS(30:59,:,:),area,1.0,1))

DELTA_N_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_1_4CO2,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))
DELTA_N_SOM_SAG_GLOBALmean = (wgt_areaave_Wrap(NET_RAD_SOM_SAG,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1_4CO2,area,1.0,1))
DELTA_N_SOM_SAG_GLOBALmean_comb = (wgt_areaave_Wrap(NET_RAD_SOM_SAG,area,1.0,1)) - (wgt_areaave_Wrap(NET_RAD_SOM_1CO2,area,1.0,1))

DELTA_T_SOM_1CO2_GLOBALmean = (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1_4CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_SAG_GLOBALmean = (wgt_areaave_Wrap(e_SAG->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1_4CO2->TS(40:99,:,:),area,1.0,1))
DELTA_T_SOM_SAG_GLOBALmean_comb = (wgt_areaave_Wrap(e_SAG->TS(40:99,:,:),area,1.0,1)) - (wgt_areaave_Wrap(e_1CO2->TS(40:99,:,:),area,1.0,1))

; Calculate lambda for each forcing type
Lamda_1CO2 = (dim_avg_n_Wrap(DELTA_N_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_1CO2_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_1CO2_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_1CO2_GLOBALmean, 0))
Lamda_SAG = (dim_avg_n_Wrap(DELTA_N_SST_SAG_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SAG_GLOBALmean, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SAG_GLOBALmean, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SAG_GLOBALmean, 0))
Lamda_SAG_comb = (dim_avg_n_Wrap(DELTA_N_SST_SAG_GLOBALmean_comb, 0) - dim_avg_n_Wrap(DELTA_N_SOM_SAG_GLOBALmean_comb, 0)) / (dim_avg_n_Wrap(DELTA_T_SST_SAG_GLOBALmean_comb, 0) - dim_avg_n_Wrap(DELTA_T_SOM_SAG_GLOBALmean_comb, 0))

; Calculate ERF for each latitude
deg2rad = 4.0 * atan(1.0) / 180.0
coslat  = cos(lat * deg2rad)

; For ERF_1CO2
ERF_zonal_1CO2 = delta_N_SST_1CO2_lat - Lamda_1CO2 * DELTA_T_SST_1CO2_lat
ERF_1CO2 = dim_sum_n(ERF_zonal_1CO2 * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_SAG
ERF_zonal_SAG = delta_N_SST_SAG_lat - Lamda_SAG * DELTA_T_SST_SAG_lat
ERF_SAG = dim_sum_n(ERF_zonal_SAG * coslat, 0) / dim_sum_n(coslat, 0)

; For ERF_SOLAR
ERF_zonal_SAG_comb = delta_N_SST_SAG_lat_comb - Lamda_SAG_comb * DELTA_T_SST_SAG_lat_comb
ERF_SAG_comb = dim_sum_n(ERF_zonal_SAG_comb * coslat, 0) / dim_sum_n(coslat, 0)

; Create a new array to hold all three ERF zonal profiles
ERF = new((/3, dimsizes(lat)/), typeof(dummy4), dummy4@_FillValue)
ERF(0,:) = ERF_zonal_1CO2
ERF(1,:) = ERF_zonal_SAG
ERF(2,:) = ERF_zonal_SAG_comb
ERF!0 = "case"
ERF!1 = "lat"
ERF&lat = lat

; Print the global mean ERF values
print("Global mean ERF values:")
print("Global warming (1.4xCO2-1xCO2): " + ERF_1CO2)
print("SAG effect (SAG-1.4xCO2): " + ERF_SAG)
print("SAG+warming effect (SAG-1CO2): " + ERF_SAG_comb)

;plotting
;***********************************************************
; Plot: Radiative Forcing vs Latitude
;***********************************************************

wks = gsn_open_wks("pdf","ERF_vs_lat")   ; or "png" instead of "pdf"

res = True
res@gsnDraw           = True
res@gsnFrame          = True
res@xyLineColors      = (/"red","blue","green"/)
res@xyLineThicknesses = (/2.5, 2.5, 2.5/)
res@xyDashPatterns    = (/0, 0, 0/)         ; all solid lines
res@gsnXYTopLabel     = False
res@tiMainString      = "Effective Radiative Forcing vs Latitude"
res@tiYAxisString     = "ERF (Wm~S~-2~NN~)"
res@tiXAxisString     = "Latitude (~F34~0~F~)"
res@trXMinF           = -90
res@trXMaxF           = 90

res@vpHeightF         = 0.6
res@vpWidthF          = 0.75

res@lgLabelFontHeightF = 0.015
res@xyExplicitLegendLabels = (/"Warmong effect (1.4xCO~B~2-1xCO~B~2)", "SAG effect (SAG - 1.4xCO~B~2)", "Combined effect (SAG - 1xCO~B~2)"/)
res@pmLegendDisplayMode = "Always"
res@pmLegendSide = "Top"     ; or "Bottom", "Top", etc.
res@pmLegendParallelPosF = 0.8 ; fine-tune positioning
res@pmLegendWidthF         =  0.18                       ;-- define legend width
res@pmLegendHeightF        =  0.15   
res@pmLegendOrthogonalPosF = -0.40
res@lgPerimOn = True           ; legend box border

plot = gsn_csm_xy(wks, lat, ERF, res)

exit()
  
end
  