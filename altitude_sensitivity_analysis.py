#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to demonstrate that precipitation is insensitive to altitude (SAG level)
while surface temperature is sensitive to altitude in a single graph.
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Data for the three SAG levels
data = {
    'SAG_Level': ['22km', '18km', '16km'],
    'SAG_Altitude': [22, 18, 16],  # For numerical plotting
    'Global_TS_Change': [-3.88, -3.64, -3.31],
    'Global_TS_Error': [0.3, 0.10, 0.10],
    'Global_PPT_Change': [-10.68, -10.33, -10.03],
    'Global_PPT_Error': [0.41, 0.43, 0.35],
    'Tropics_TS_Change': [-3.23, -3.06, -2.73],
    'Tropics_TS_Error': [0.13, 0.12, 0.12],
    'Tropics_PPT_Change': [-8.25, -7.96, -7.86],
    'Tropics_PPT_Error': [0.59, 0.66, 0.59]
}

df = pd.DataFrame(data)

# Create figure with dual y-axes
fig, ax1 = plt.subplots(figsize=(12, 8))

# Plot temperature on left y-axis
color_temp = 'tab:red'
ax1.set_xlabel('SAG Level (km)', fontsize=12, fontweight='bold')
ax1.set_ylabel('Temperature Change (K)', color=color_temp, fontsize=12, fontweight='bold')

# Global temperature
line1 = ax1.errorbar(df['SAG_Altitude'], df['Global_TS_Change'], 
                     yerr=df['Global_TS_Error'], 
                     color=color_temp, marker='o', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Global Temperature', linestyle='-')

# Tropics temperature  
line2 = ax1.errorbar(df['SAG_Altitude'], df['Tropics_TS_Change'], 
                     yerr=df['Tropics_TS_Error'], 
                     color='darkred', marker='s', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Tropics Temperature', linestyle='--')

ax1.tick_params(axis='y', labelcolor=color_temp)
ax1.grid(True, alpha=0.3)

# Create second y-axis for precipitation
ax2 = ax1.twinx()
color_ppt = 'tab:blue'
ax2.set_ylabel('Precipitation Change (%)', color=color_ppt, fontsize=12, fontweight='bold')

# Global precipitation
line3 = ax2.errorbar(df['SAG_Altitude'], df['Global_PPT_Change'], 
                     yerr=df['Global_PPT_Error'], 
                     color=color_ppt, marker='^', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Global Precipitation', linestyle='-')

# Tropics precipitation
line4 = ax2.errorbar(df['SAG_Altitude'], df['Tropics_PPT_Change'], 
                     yerr=df['Tropics_PPT_Error'], 
                     color='darkblue', marker='D', markersize=8, linewidth=2.5,
                     capsize=5, capthick=2, label='Tropics Precipitation', linestyle='--')

ax2.tick_params(axis='y', labelcolor=color_ppt)

# Customize x-axis
ax1.set_xticks(df['SAG_Altitude'])
ax1.set_xticklabels(df['SAG_Level'])
ax1.invert_xaxis()  # Higher resolution (lower km) on the right

# Add title and legend
plt.title('Impact of injection altitude on surface temperature and precipitation responses', 
          fontsize=14, fontweight='bold', pad=20)

# Combine legends from both axes
lines1, labels1 = ax1.get_legend_handles_labels()
lines2, labels2 = ax2.get_legend_handles_labels()
ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=2)

# Add text annotations to highlight the key finding
ax1.text(0.98, 0.02, 'Temperature: Strong altitude dependence\nPrecipitation: Weak altitude dependence', 
         transform=ax1.transAxes, fontsize=11, fontweight='bold',
         verticalalignment='bottom', horizontalalignment='right',
         bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

plt.tight_layout()

# Save the figure
plt.savefig('altitude_sensitivity_analysis.png', dpi=300, bbox_inches='tight')
plt.savefig('altitude_sensitivity_analysis.pdf', bbox_inches='tight')

# Calculate and print sensitivity metrics
print("\n" + "="*70)
print("ALTITUDE SENSITIVITY ANALYSIS")
print("="*70)

# Calculate coefficient of variation (CV = std/mean) for each variable
global_temp_cv = np.std(np.abs(df['Global_TS_Change'])) / np.mean(np.abs(df['Global_TS_Change'])) * 100
global_ppt_cv = np.std(np.abs(df['Global_PPT_Change'])) / np.mean(np.abs(df['Global_PPT_Change'])) * 100
tropics_temp_cv = np.std(np.abs(df['Tropics_TS_Change'])) / np.mean(np.abs(df['Tropics_TS_Change'])) * 100
tropics_ppt_cv = np.std(np.abs(df['Tropics_PPT_Change'])) / np.mean(np.abs(df['Tropics_PPT_Change'])) * 100

print(f"Coefficient of Variation (CV) - Higher values indicate more sensitivity:")
print(f"  Global Temperature:     {global_temp_cv:.1f}%")
print(f"  Global Precipitation:   {global_ppt_cv:.1f}%")
print(f"  Tropics Temperature:    {tropics_temp_cv:.1f}%")
print(f"  Tropics Precipitation:  {tropics_ppt_cv:.1f}%")

# Calculate range (max - min) as another sensitivity metric
global_temp_range = max(np.abs(df['Global_TS_Change'])) - min(np.abs(df['Global_TS_Change']))
global_ppt_range = max(np.abs(df['Global_PPT_Change'])) - min(np.abs(df['Global_PPT_Change']))
tropics_temp_range = max(np.abs(df['Tropics_TS_Change'])) - min(np.abs(df['Tropics_TS_Change']))
tropics_ppt_range = max(np.abs(df['Tropics_PPT_Change'])) - min(np.abs(df['Tropics_PPT_Change']))

print(f"\nRange (Max - Min) across SAG levels:")
print(f"  Global Temperature:     {global_temp_range:.2f} K")
print(f"  Global Precipitation:   {global_ppt_range:.2f} %")
print(f"  Tropics Temperature:    {tropics_temp_range:.2f} K")
print(f"  Tropics Precipitation:  {tropics_ppt_range:.2f} %")

print(f"\nConclusion:")
print(f"Temperature shows {global_temp_cv/global_ppt_cv:.1f}x more sensitivity to altitude than precipitation (Global)")
print(f"Temperature shows {tropics_temp_cv/tropics_ppt_cv:.1f}x more sensitivity to altitude than precipitation (Tropics)")

plt.show()
