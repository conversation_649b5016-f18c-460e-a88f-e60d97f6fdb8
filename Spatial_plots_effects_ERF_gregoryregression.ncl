;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
 f1 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F_CO2_01_60_new1.nc", "r")      ; 1×CO2
  f2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F2000_2CO2_01_60_560_2D_1.nc", "r") ; 2×CO2
  f3= addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_F2000_65_90_560_01_60_2D_1.nc", "r") ; 2×CO2 + Polar SAG



  ; Read in variables (assumed to be time,lat,lon)
  ts1  = f1->TS(30:59,:,:)
  fsnt1 = f1->FSNT(30:59,:,:)
  flnt1 = f1->FLNT(30:59,:,:)
  ts2  = f2->TS(30:59,:,:)
  fsnt2 = f2->FSNT(30:59,:,:)
  flnt2 = f2->FLNT(30:59,:,:)

  ts3  = f3->TS(30:59,:,:)
  fsnt3 = f3->FSNT(30:59,:,:)
  flnt3 = f3->FLNT(30:59,:,:)

  ; Calculate net TOA radiation
  net1 = fsnt1 - flnt1
  net2 = fsnt2 - flnt2
  net3 = fsnt3 - flnt3

  ; Latitude and longitude
  lat = f1->lat
  lon = f1->lon

  ; Calculate area weights
  lat=f1->lat
lon=f1->lon

  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************   
dummy3=f1->lat
copy_VarCoords(dummy3,area)
;wgt2d = conform(ts1_1, wgt, 0)  ; replicate wgt(lat) along lon dimension to get [lat,lon]

  ; Global mean TS and TOA radiation (time series)
  printVarSummary(ts1)
 
;wgt2d = conform(ts1_1, wgt, 0)  ; replicate wgt(lat) along lon dimension to get [lat,lon]

  ts1_g = wgt_areaave_Wrap(ts1, area, 1.0, 0)
  ts2_g = wgt_areaave_Wrap(ts2, area, 1.0, 0)
  ts3_g = wgt_areaave_Wrap(ts3, area, 1.0, 0)


  net1_g = wgt_areaave(net1, area, 1.0, 0)
  net2_g = wgt_areaave(net2, area, 1.0, 0)
  net3_g = wgt_areaave(net3, area, 1.0, 0)

  ; Compute deltas
  delta_ts_warming = ts2_g - ts1_g
  delta_n_warming  = net2_g - net1_g

  delta_ts_sag = ts3_g - ts2_g
  delta_n_sag  = net3_g - net2_g

  delta_ts_comb = ts3_g - ts1_g
  delta_n_comb  = net3_g - net1_g

  ; Regression fits
  reg_warm = regline(delta_ts_warming, delta_n_warming)
  print(reg_warm)
  lambda_warm = -reg_warm(0)
  erf_warm = reg_warm@yintercept

  reg_sag = regline(delta_ts_sag, delta_n_sag)
  lambda_sag = -reg_sag(0)
  erf_sag = reg_sag@yintercept

  reg_comb = regline(delta_ts_comb, delta_n_comb)
  print(reg_comb)
  lambda_comb = -reg_comb(0)
  print(lambda_comb)
  erf_comb = reg_comb@yintercept

  print("Gregory Regression Results:")
  print("Warming (1.4xCO2 - 1xCO2): ERF = " + erf_warm + " W/m2, lambda = " + lambda_warm + " W/m2/K")
  print("SAG effect (SAG - 1.4xCO2): ERF = " + erf_sag + " W/m2, lambda = " + lambda_sag + " W/m2/K")
  print("Combined (SAG - 1xCO2): ERF = " + erf_comb + " W/m2, lambda = " + lambda_comb + " W/m2/K")

  ; Optional plot
  wks = gsn_open_wks("pdf","gregory_regression")
  res = True
  res@xyMarkLineModes = "Markers"
  res@tiMainString = "Gregory Regression: Net TOA vs Global ΔT"
  res@tiXAxisString = "Global ΔT (K)"
  res@tiYAxisString = "Net TOA Flux (W/m2)"
  res@xyLineColors = (/"red","blue","green"/)
  res@xyLineThicknesses = (/2,2,2/)

  plot = gsn_csm_xy(wks, \
                    (/delta_ts_warming, delta_ts_sag, delta_ts_comb/), \
                    (/delta_n_warming, delta_n_sag, delta_n_comb/), res)
end

;(0)     Warming (1.4xCO2 - 1xCO2): ERF = 1.78941 W/m2, lambda = 0.349494 W/m2/K
;(0)     SAG effect (SAG - 1.4xCO2): ERF = -0.567034 W/m2, lambda = -0.386056 W/m2/K
;(0)     Combined (SAG - 1xCO2): ERF = 1.05482 W/m2, lambda = 0.876859 W/m2/K