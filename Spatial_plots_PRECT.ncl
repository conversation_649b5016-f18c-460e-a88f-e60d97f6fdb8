;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
;load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/home/<USER>/miniconda3/pkgs/ncl-6.6.2-h7cb714c_54/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"


begin
  ;--- Read files
  f1 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E_CO2_01_100new1.nc", "r")      ; 1×CO2
  ;f2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") ; 2×CO2
  f3 = addfile("/home/<USER>/Documents/backup/my_work_draft/writeups/hydrological_cycle/Yearly_E_2CO2_01_100new1.nc", "r") ; 2×CO2
f2 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r") ; 1.4×CO2

  f4 = addfile("/home/<USER>/Documents/backup/my_work_draft/Forcing_Files/Final/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r") ; 2×CO2 + Polar SAG

  ;--- Extract surface temperature variable
  ts_1co2 = f1->PRECT*8.64e+7
  ts_1_4co2 = f2->PRECT*8.64e+7
  ts_2co2 = f3->PRECT*8.64e+7
  ts_pole = f4->PRECT*8.64e+7

  ;--- Time average [time, lat, lon] → [lat, lon]
  ts_1co2_avg = dim_avg_n_Wrap(ts_1co2, 0)
    ts_1_4co2_avg = dim_avg_n_Wrap(ts_1_4co2, 0)
  ts_2co2_avg = dim_avg_n_Wrap(ts_2co2, 0)
  ts_pole_avg = dim_avg_n_Wrap(ts_pole, 0)

  ; Calculate global and tropical mean temperature changes using wgt_areaave_Wrap
  lat=f1->lat
  rad = 4.0*atan(1.0)/180.0
  wgt = cos(lat * rad)

  ; Global mean (all latitudes)
  ts_diff_global1 = (wgt_areaave_Wrap(ts_1co2_avg, wgt, 1.0, 0))
  ts_diff_global2 = (wgt_areaave_Wrap(ts_1_4co2_avg, wgt, 1.0, 0))
  ts_diff_global3 = (wgt_areaave_Wrap(ts_2co2_avg, wgt, 1.0, 0))
    ts_diff_global4 = (wgt_areaave_Wrap(ts_pole_avg, wgt, 1.0, 0))

 ; lat_indices = ind(lat.ge.-30 .and. lat.le.30)
 ; ts_diff_tropical_UNIF = wgt_areaave_Wrap(TS_diff_UNIF(lat_indices,:), wgt(lat_indices), 1.0, 0)

  Label_Global_mean1 = "Global Mean="+decimalPlaces(ts_diff_global1,2,True)+ "mm/day"
  ;Label_Tropical_mean_UNIF = "Tropical Mean="+decimalPlaces(ts_diff_tropical_UNIF,2,True)+ " K"
  Label_Global_mean2 = "Global Mean="+decimalPlaces(ts_diff_global2,2,True)+ "mm/day"
  Label_Global_mean3 = "Global Mean="+decimalPlaces(ts_diff_global3,2,True)+ "mm/day"
Label_Global_mean4 = "Global Mean="+decimalPlaces(ts_diff_global4,2,True)+ "mm/day"

  print("Global mean precipitation (1CO2): " + ts_diff_global1 + " mm/day")
  print("Global mean precipitation (1.4CO2): " + ts_diff_global2 + " mm/day")
  print("Global mean precipitation (2CO2): " + ts_diff_global3 + "  mm/day")
  print("Global mean precipitation (SAG): " + ts_diff_global4+ "  mm/day")

  ;--- Open workstation
  wks = gsn_open_wks("png", "PRECT_spatial_panels")   ; Output: TS_spatial_panels.png

  ;--- Define color map and plotting options
  gsn_define_colormap(wks, "WhiteBlue")
;gsn_reverse_colormap(wks)
  res                     = True
  res@gsnDraw             = False
  res@gsnFrame            = False
  res@cnFillOn            = True
  res@cnLinesOn           = False
  res@cnLineLabelsOn      = False
  res@gsnAddCyclic        = True
  res@cnFillMode          = "AreaFill"
  res@gsnLeftString       = ""
  res@gsnRightString      = ""

  ;--- Common color bounds for comparison (adjust as needed)
   res@cnLevelSelectionMode = "ManualLevels"
  res@cnMinLevelValF      = 0
  res@cnMaxLevelValF      = 10
  res@cnLevelSpacingF     = 2
  res@lbLabelBarOn = False  ; Turn off individual label bars
  res@tiMainFontHeightF   = 0.015

  ;--- Titles
  res@gsnCenterString     = "1xCO~B~2"
    res@gsnRightString = Label_Global_mean1
  plot0 = gsn_csm_contour_map_ce(wks, ts_1co2_avg, res)

    res@gsnCenterString     = "1.4xCO~B~2"
        res@gsnRightString = Label_Global_mean2
  plot1 = gsn_csm_contour_map_ce(wks, ts_1_4co2_avg, res)

  res@gsnCenterString     = "2xCO~B~2"
      res@gsnRightString = Label_Global_mean3
  plot2 = gsn_csm_contour_map_ce(wks, ts_2co2_avg, res)

  res@gsnCenterString     = "1.4xCO~B~2~N~ + Polar~B~SAG~"
      res@gsnRightString = Label_Global_mean4
  plot3 = gsn_csm_contour_map_ce(wks, ts_pole_avg, res)

  ;--- Panel plots
  pres                  = True
  pres@gsnMaximize      = True
  pres@gsnPanelLabelBar = True
  pres@lbLabelFontHeightF = 0.012

  gsn_panel(wks, (/plot0, plot1, plot2, plot3/), (/2,2/), pres)

end
