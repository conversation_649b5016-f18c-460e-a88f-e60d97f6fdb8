load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
undf =-999.999
;file_SAmes
; case=(/"CO2_01_100_2D_1","2CO2_01_100_2D_1", "2CO2_UNIF_01_100_2D_1", "2CO2_TROP_01_100_2D_1", "2CO2_POLAR_01_100_2D_1","2CO2_ARCTIC_01_100_2D_1", "2CO2_ANTARC_01_100_2D_1"/)
case=(/"E_CO2_01_100new1","E_2CO2_01_100new1", "E2000_2CO2_UNIF_01_100_2D_1",  "E2000_2CO2_POLAR_01_100_2D_1"/)

pc=new((/7,60,96,144/),"float",undf)
pl=new((/7,60,96,144/),"float",undf)
PT=new((/7,60,96,144/),"float",undf)
PT_masked=new((/7,60,96,144/),"float",undf)
dummy=new((/7,60,96,144/),"float",undf)
landsea=new((/7,60,96,144/),"float",undf)
SAf_PT=new((/7,60/),"float",undf)
SA_PT=new((/7,60/),"float",undf)
AUS_PT=new((/7,60/),"float",undf)
SHMI=new((/7,60/),"float",undf)

do i= 0,dimsizes(case)-1
var1="DJF_Yearly"
file_path="/home/<USER>/Documents/Data/DJF/"
; file_path="/Volumes/anuhdd2/Data/22_5MT/Yearly_som/"
fname= var1+"_"+case(i)+".nc"
fils =  systemfunc("ls "+file_path+fname)
s    = addfile(fils,"r")
pc(i,:,:,:)=s->PRECC(40:99,:,:)
pl(i,:,:,:)=s->PRECC(40:99,:,:)
PT(i,:,:,:)=((s->PRECC(40:99,:,:)*(8.64e+7))+(s->PRECL(40:99,:,:))*(8.64e+7))
dummy(i,:,:,:)=s->PRECC(40:99,:,:)
copy_VarCoords(dummy, PT)

landsea(i,:,:,:)         = s->LANDFRAC(40:99,:,:)   
printVarSummary(landsea)
PT_masked= where(landsea.gt.0,PT,undf)
copy_VarCoords(dummy, PT_masked)
;masked data
; North African monsoon-------------------------------------------------SAf
SAf_PT(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{-35:-5},{10:50}),(/1,2/)) 

; North American monsoon-------------------------------------------------SA
SA_PT(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{-35:-5},{280:330}),(/1,2/))

;South Asian monsoon-------------------------------------------------AUS
AUS_PT(i,:)=dim_avg_n_Wrap(PT_masked(i,:,{-25:-5},{110:155}),(/1,2/)) 

SHMI(i,:)=((SAf_PT(i,:)+SA_PT(i,:)+AUS_PT(i,:)))/3
end do
; printVarSummary(PT)
; printVarSummary(SAf_PT)
; printVarSummary(SHMI)
; print(SAf_PT(0,1))
; print(SA_PT(0,1))
; print(AUS_PT(0,1))
; print(SHMI(0,1))
; print(PT(0,1,1,1))
a=dim_avg_n(SHMI(0,:),0)
b=dim_avg_n(SHMI(1,:),0)
; c=dim_avg_n(SHMI(2,:),0)
; d=dim_avg_n(SHMI(3,:),0)
; e=dim_avg_n(SHMI(4,:),0)
; f=dim_avg_n(SHMI(5,:),0)
; g=dim_avg_n(SHMI(6,:),0)
; h=dim_avg_n(SHMI(6,:),0)

print(a)
print(b)
; print(c)
; print(d)
; print(e)
; print(f)
; print(g)
; print(h)


; asciiwrite ("SAf_PT_1CO2.txt" , sprintf("%9.3f", SAf_PT(0,:)))
; asciiwrite ("SAf_PT_2CO2.txt" , sprintf("%9.3f", SAf_PT(1,:)))
; asciiwrite ("SAf_PT_UNIF.txt" , sprintf("%9.3f", SAf_PT(2,:)))
; asciiwrite ("SAf_PT_TROP.txt" , sprintf("%9.3f", SAf_PT(3,:)))
; asciiwrite ("SAf_PT_POLA.txt" , sprintf("%9.3f", SAf_PT(4,:)))
; asciiwrite ("SAf_PT_ARCT.txt" , sprintf("%9.3f", SAf_PT(5,:)))
; asciiwrite ("SAf_PT_ANTA.txt" , sprintf("%9.3f", SAf_PT(6,:)))

; asciiwrite ("SA_PT_1CO2.txt" , sprintf("%9.3f", SA_PT(0,:)))
; asciiwrite ("SA_PT_2CO2.txt" , sprintf("%9.3f", SA_PT(1,:)))
; asciiwrite ("SA_PT_UNIF.txt" , sprintf("%9.3f", SA_PT(2,:)))
; asciiwrite ("SA_PT_TROP.txt" , sprintf("%9.3f", SA_PT(3,:)))
; asciiwrite ("SA_PT_POLA.txt" , sprintf("%9.3f", SA_PT(4,:)))
; asciiwrite ("SA_PT_ARCT.txt" , sprintf("%9.3f", SA_PT(5,:)))
; asciiwrite ("SA_PT_ANTA.txt" , sprintf("%9.3f", SA_PT(6,:)))

; asciiwrite ("AUS_PT_1CO2.txt" , sprintf("%9.3f", AUS_PT(0,:)))
; asciiwrite ("AUS_PT_2CO2.txt" , sprintf("%9.3f", AUS_PT(1,:)))
; asciiwrite ("AUS_PT_UNIF.txt" , sprintf("%9.3f", AUS_PT(2,:)))
; asciiwrite ("AUS_PT_TROP.txt" , sprintf("%9.3f", AUS_PT(3,:)))
; asciiwrite ("AUS_PT_POLA.txt" , sprintf("%9.3f", AUS_PT(4,:)))
; asciiwrite ("AUS_PT_ARCT.txt" , sprintf("%9.3f", AUS_PT(5,:)))
; asciiwrite ("AUS_PT_ANTA.txt" , sprintf("%9.3f", AUS_PT(6,:)))

; asciiwrite ("SHMI_1CO2_masked1.txt" , sprintf("%9.3f", SHMI(0,:)))
; asciiwrite ("SHMI_2CO2_masked1.txt" , sprintf("%9.3f", SHMI(1,:)))
; asciiwrite ("SHMI_UNIF_masked_22_5_1.txt" , sprintf("%9.3f", SHMI(2,:)))
; asciiwrite ("SHMI_TROP_masked_22_5_1.txt" , sprintf("%9.3f", SHMI(3,:)))
; asciiwrite ("SHMI_POLA_masked_22_5_1.txt" , sprintf("%9.3f", SHMI(4,:)))
; asciiwrite ("SHMI_ARCT_masked_22_5_1.txt" , sprintf("%9.3f", SHMI(5,:)))
; asciiwrite ("SHMI_ANTA_masked_22_5_1.txt" , sprintf("%9.3f", SHMI(6,:)))

; asciiwrite ("SHMI_1CO2_masked.txt" , SHMI(0,:))
; asciiwrite ("SHMI_2CO2_masked.txt" , SHMI(1,:))
; asciiwrite ("SHMI_UNIF_masked_22_5.txt" , SHMI(2,:))
; asciiwrite ("SHMI_TROP_masked_22_5.txt", SHMI(3,:))
; asciiwrite ("SHMI_POLA_masked_22_5.txt" , SHMI(4,:))
; asciiwrite ("SHMI_ARCT_masked_22_5.txt" , SHMI(5,:))
; asciiwrite ("SHMI_ANTA_masked_22_5.txt" , SHMI(6,:))



; asciiwrite ("SAf_1CO2_masked_new.txt" , SAf_PT(0,:))
; asciiwrite ("SAf_2CO2_masked_new.txt" ,  SAf_PT(1,:))
; asciiwrite ("SAf_UNIF_masked_22_5_new_2lev.txt" ,  SAf_PT(2,:))
; asciiwrite ("SAf_TROP_masked_22_5_new_2lev.txt" ,  SAf_PT(3,:))
; asciiwrite ("SAf_POLA_masked_22_5_new_2lev.txt" , SAf_PT(4,:))
; asciiwrite ("SAf_ARCT_masked_22_5_new_2lev.txt" , SAf_PT(5,:))
; asciiwrite ("SAf_ANTA_masked_22_5_new_2lev.txt" ,  SAf_PT(6,:))

; asciiwrite ("SA_1CO2_masked_new.txt" ,  SA_PT(0,:))
; asciiwrite ("SA_2CO2_masked_new.txt" ,  SA_PT(1,:))
; asciiwrite ("SA_UNIF_masked_22_5_new_2lev.txt" ,  SA_PT(2,:))
; asciiwrite ("SA_TROP_masked_22_5_new_2lev.txt" ,  SA_PT(3,:))
; asciiwrite ("SA_POLA_masked_22_5_new_2lev.txt" ,  SA_PT(4,:))
; asciiwrite ("SA_ARCT_masked_22_5_new_2lev.txt" , SA_PT(5,:))
; asciiwrite ("SA_ANTA_masked_22_5_new_2lev.txt" ,  SA_PT(6,:))

; asciiwrite ("AUS_1CO2_masked_new.txt" ,  AUS_PT(0,:))
; asciiwrite ("AUS_2CO2_masked_new.txt" ,  AUS_PT(1,:))
; asciiwrite ("AUS_UNIF_masked_22_5_new_2lev.txt" ,  AUS_PT(2,:))
; asciiwrite ("AUS_TROP_masked_22_5_new_2lev.txt" , AUS_PT(3,:))
; asciiwrite ("AUS_POLA_masked_22_5_new_2lev.txt" ,  AUS_PT(4,:))
; asciiwrite ("AUS_ARCT_masked_22_5_new_2lev.txt" , AUS_PT(5,:))
; asciiwrite ("AUS_ANTA_masked_22_5_new_2lev.txt" ,  AUS_PT(6,:))


asciiwrite ("SHMI_1CO2_masked_new.txt" , SHMI(0,:))
asciiwrite ("SHMI_2CO2_masked_new.txt" , SHMI(1,:))
asciiwrite ("SHMI_UNIF_masked_22_5_new_2lev.txt" , SHMI(2,:))
asciiwrite ("SHMI_POLA_masked_22_5_new_2lev.txt" , SHMI(3,:))
asciiwrite ("SHMI_POLES_masked_53_72_new_1lev.txt" , SHMI(4,:))
end
