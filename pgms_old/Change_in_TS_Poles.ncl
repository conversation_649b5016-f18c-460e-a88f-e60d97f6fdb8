load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/contributed.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "$NCARG_ROOT/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"



;---Recreating jpeg images only works for X11 and PNG.
;---Recreating jpeg images only works for X11 and PNG.
  ;  wks_type = "png"
   wks_type = "pdf"

   wks_type@wkWidth = 2500
   wks_type@wkHeight = 2500 
   pname = "plot_TS_change_POLES"
  ;  wks = gsn_open_wks("eps",pname)
  ;  wks = gsn_open_wks("pdf",pname)
  ;  wks = gsn_open_wks("png",pname)

   wks = gsn_open_wks("pdf",pname)

;_______________________________________________________________________________________



begin
;*************************************************

e_1CO2 = addfile("/home/<USER>/Documents/newdata_pgms/Yearly_E_CO2_01_100new.nc", "r")
e_2CO2 = addfile("/home/<USER>/Documents/newdata_pgms/Yearly_E2000_2CO2_01_100new1.nc", "r")
;65-90 degrees
e_POLES_25_76Mt = addfile("/user1/anu_x/New_forcing_try/Data/E_POLES_1lev_25_76/Yearly_E2000_POLES_01_50_25_76_1lev_1.nc", "r");65-90 degree aerosols distribution
e_POLES_53_72Mt = addfile("/user1/anu_x/New_forcing_try/Data/Final/E2000_POLES_65_90_53Mt/hist/Yearly_E2000_POLES_65_90_01_100_53_1.nc", "r")

 

TS_e_1CO2=e_1CO2->TS(40:99,:,:)
TS_e_2CO2=e_2CO2->TS(40:99,:,:)
TS_e_POLES_25=e_POLES_25_76Mt->TS(40:49,:,:)
TS_e_POLES_53=e_POLES_53_72Mt->TS(40:99,:,:)



;---------------------------------------------------------
dummy=e_1CO2->TS(0:3,:,:)
;dummy@_FillValue = 1e+36
dummy@_FillValue =-999.999

;dummy@_FillValue = getFillValue(TS_e_1CO2)
;print(getFillValue(TS_e_1CO2))

TS_mean=new((/4,96,144/),typeof(dummy),dummy@_FillValue)
TS_mean(0,:,:)=dim_avg_n_Wrap(TS_e_2CO2,0)
TS_mean(1,:,:)=dim_avg_n_Wrap(TS_e_1CO2,0)
TS_mean(2,:,:)=dim_avg_n_Wrap(TS_e_POLES_25,0)
TS_mean(3,:,:)=dim_avg_n_Wrap(TS_e_POLES_53,0)

copy_VarCoords(dummy,TS_mean)
printVarSummary(TS_mean)

TS_variance=new((/4,96,144/),typeof(dummy),dummy@_FillValue)
TS_variance(0,:,:)=dim_variance_n_Wrap(TS_e_2CO2,0)
TS_variance(1,:,:)=dim_variance_n_Wrap(TS_e_1CO2,0)
TS_variance(2,:,:)=dim_variance_n_Wrap(TS_e_POLES_25,0)
TS_variance(3,:,:)=dim_variance_n_Wrap(TS_e_POLES_53,0)

copy_VarCoords(dummy,TS_variance)
;---------------------------------------------------------------
dummy2=e_1CO2->TS(0:2,:,:)
dummy2@_FillValue =-999.999

Change_TS=new((/3,96,144/),typeof(dummy2),dummy2@_FillValue)
Change_TS(0,:,:)=TS_mean(1,:,:)-TS_mean(0,:,:)
Change_TS(1,:,:)=TS_mean(2,:,:)-TS_mean(0,:,:)
Change_TS(2,:,:)=TS_mean(3,:,:)-TS_mean(0,:,:)

copy_VarCoords(dummy2,Change_TS)



;write mask array to new file
;system("rm -f alpha.nc")
;fout = addfile("alpha.nc","c")
;fout->alpha   = alpha
;fout->prob = prob_TS
;fout->TS_variance = TS_variance
;fout->Change_TS=Change_TS
;fout->TS_mean=TS_mean
 

;---------------------------------------------------------

;******************************************                 Areal average
lat=e_1CO2->lat
lon=e_1CO2->lon


  jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
;****************************************************       
print(lat)
dummy4=e_1CO2->TS(0:3,82:95,:)
dummy4@_FillValue =-999.999

; GlobalMean_1CO2_e_rest          = wgt_areaave_Wrap(TS_mean(1,14:81,:),area(14:81),1.0,1)
; ; copy_VarCoords(dummy4, GlobalMean_1CO2_e_rest)
GlobalMean_2CO2_e_rest          = wgt_areaave_Wrap(TS_mean(0,14:81,:),area(14:81),1.0,1)
; GlobalMean_POL_25_e_rest          = wgt_areaave_Wrap(TS_mean(2,14:81,:),area(14:81),1.0,1)
; GlobalMean_POL_53_e_rest          = wgt_areaave_Wrap(TS_mean(3,14:81,:),area(14:81),1.0,1)

; print(GlobalMean_2CO2_e_rest)

; GlobalMean_1CO2_e_NH          = wgt_areaave_Wrap(TS_mean(1,82:95,:),area(82:95),1.0,1)
; ; copy_VarCoords(dummy4, GlobalMean_1CO2_e_rest)
; GlobalMean_2CO2_e_NH          = wgt_areaave_Wrap(TS_mean(0,82:95,:),area(82:95),1.0,1)
; GlobalMean_POL_25_e_NH          = wgt_areaave_Wrap(TS_mean(2,82:95,:),area(82:95),1.0,1)
; GlobalMean_POL_53_e_NH          = wgt_areaave_Wrap(TS_mean(3,82:95,:),area(82:95),1.0,1)
; print(GlobalMean_1CO2_e_NH)


GlobalMean_1CO2_e_SH          = wgt_areaave_Wrap(TS_mean(1,0:13,:),area(0:13),1.0,1)
; copy_VarCoords(dummy4, GlobalMean_1CO2_e_rest)
GlobalMean_2CO2_e_SH          = wgt_areaave_Wrap(TS_mean(0,0:13,:),area(0:13),1.0,1)
GlobalMean_POL_25_e_SH          = wgt_areaave_Wrap(TS_mean(2,0:13,:),area(0:13),1.0,1)
GlobalMean_POL_53_e_SH          = wgt_areaave_Wrap(TS_mean(3,0:13,:),area(0:13),1.0,1)
print(GlobalMean_POL_53_e_SH)
; GlobalMean_Change_TS_NH=new((/4/),typeof(dummy4),dummy4@_FillValue)



Global_mean=wgt_areaave_Wrap(TS_mean,area,1.0,0)
copy_VarCoords(dummy2,Global_mean)

; print(Global_mean)

dummy3=e_1CO2->TS(0:2,0,0)
GlobalMean_Change_TS=new((/6/),typeof(dummy3),dummy3@_FillValue)
GlobalMean_Change_TS(0)=Global_mean(1)-Global_mean(0)
GlobalMean_Change_TS(1)=Global_mean(2)-Global_mean(0)
GlobalMean_Change_TS(2)=Global_mean(3)-Global_mean(0)


copy_VarCoords(dummy2,GlobalMean_Change_TS)


;---------------------------------------------------------------
    
Label_Global_mean=(""+decimalPlaces(GlobalMean_Change_TS,2,True)+"K")
; print(Label_Global_mean)

;------------------------------------------------------------------------------------------------------------------------------- 


;------------------------------------------------ 

;___________________________________________________________________________________________________________________

  ;gsn_define_colormap(wks,"testcmap") ; GMT_relief_oceanonly cmp_b2r
  ; gsn_define_colormap(wks,"WhiteBlue") ; GMT_relief_oceanonly ViBlGrWhYeOrRe
  gsn_define_colormap(wks,"MPL_Blues") ; GMT_relief_oceanonly ViBlGrWhYeOrRe

  gsn_reverse_colormap(wks)

  cnres                             = True
  cnres@gsnMaximize                 = False
  ;cnres@cnFillDrawOrder             = "PreDraw"       ; draw contours before continents
  cnres@gsnDraw                     = False
  cnres@gsnFrame                    = False
  cnres@cnLinesOn                   = False
  cnres@cnLineThicknessF            = 0.5
  cnres@cnLineLabelsOn              = False
  cnres@cnFillOn                    = True

  ;cnres@mpFillOn                    = False
  ;cnres@mpGeophysicalLineColor      = "black"
  ;cnres@mpGeophysicalLineThicknessF = 1
  cnres@mpLandFillColor              = "white" ;"cornsilk" ;"black"      ;darkolivegreen ;goldenrod4
  ;cnres@mpLandFillPattern           = 4
  cnres@gsnAddCyclic                 = True

  ; cnres@mpCenterLonF                  =180
  ;cnres@mpLimitMode                  = "LatLon"
  ;cnres@mpMinLatF                    = -60
  ;cnres@mpMaxLatF                    = 60
  ;cnres@mpMinLonF                    = 0
  ;cnres@mpMaxLonF                    = 360
  cnres@cnLevelSelectionMode         = "ManualLevels"
  cnres@cnMinLevelValF               = -8.0
  cnres@cnMaxLevelValF               = 0.0
  cnres@cnLevelSpacingF              = 0.5
  cnres@lbLabelStride                = 2
  cnres@gsnRightStringFontHeightF    = -0.033
  cnres@gsnLeftStringFontHeightF     = -0.033

  cnres@gsnRightString               = ""
  cnres@gsnLeftString                = ""
  cnres@tiMainString                 = "" ;
  cnres@tiMainFont                   = "times-roman"
  cnres@lbLabelBarOn                 = False            ; turn off individual cb
  cnres@lbBoxEndCapStyle             = "TriangleBothEnds"
  ;cnres@lbBoxEndCapStyle            = "TriangleHighEnd"
  cnres@pmLabelBarWidthF             = 0.04
  cnres@pmLabelBaSSTeightF           = 0.23
  cnres@lbOrientation                = "Vertical"     ; vertical label bar

  cnres@pmTickMarkDisplayMode = "Always"            ; turn on built-in tickmarks
  cnres@lbTitleOn             = True
  cnres@lbLabelStride         = 2
  cnres@lbTitleString         = "P (mm/day)" ;"kJ/cm^2"
  cnres@lbTitleFontHeightF    = 0.022
  cnres@lbTitleFont           = "times-roman"

  cnres@tmXBLabelFontHeightF    = 0.025    ; Make these labels smaller.
  cnres@tmYLLabelFontHeightF    = 0.025    ; Make these labels smaller.


  cnres@tiYAxisFont 	     = "times-roman"
  cnres@tiXAxisFont          = "times-roman"


  
  ;  cnres@tmXBMajorLengthF     = 0.01
  ;  cnres@tmYLMajorLengthF     = 0.01
   cnres@tmXBMajorLengthF     = -0.010


  cnres@tmYLLabelFont                  = "times-roman"
  cnres@tmXBLabelFont                  = "times-roman"
  cnres@tmXBLabelsOn                   = True ;False

  ; cnres@tmYRBorderOn = False
  ; cnres@tmYLBorderOn = False
  ; cnres@tmXBBorderOn = False
  ; cnres@tmXTBorderOn = False

  cnres@tmYRLabelsOn = True
  ; cnres@tmXBMajorLengthF=0.03
  ; cnres@tmYRMajorLengthF=0.03
  cnres@tmXBOn               = True;False     ; Turn off top tickmarks
  cnres@tmXTOn               = False     ; Turn off top tickmarks
  ;cnres@tmYLOn               = False     ; Turn off left tickmarks
  ;cnres@tmYROn               = True      ; Turn off bottom tickmarks

  ; cnres@tmXBMajorOutwardLengthF =-0.02
  ; cnres@tmXBLabelStride =2
  ; cnres@tmYLLabelStride =2


  ;cnres@gsnLeftString        = ""
  ;cnres@gsnLeftStringFontHeightF = 0.019


  ;cnres@gsnZonalMean                = True         ; add zonal plot
  ;cnres@gsnZonalMeanXMinF           = -1.          ; set minimum X-axis value for zonal mean plot  
  ;cnres@gsnZonalMeanXMaxF           = 1.           ; set maximum X-axis value for zonal mean plot  
  ;cnres@gsnZonalMeanYRefLine        = 0.0          ; set reference line X-axis value



  ;cnres@vpWidthF              =  1.0
  ;cnres@vpHeightF             =  1.0
  ;cnres@vpXF                  =  0.05
  ;cnres@vpYF                  =  0.95

  dlon = 30
  dlat = 30

  ; cnres@mpProjection          = "Robinson"
  cnres@mpProjection          = "Cylindrical Equidistant"

  cnres@mpPerimOn             =  True
  ; cnres@mpGridAndLimbOn       =  True
  ; cnres@mpGridLatSpacingF     =  dlat
  ; cnres@mpGridLonSpacingF     =  dlon
  cnres@mpGridLatSpacingF     =  60
  cnres@mpGridLonSpacingF     =  60
  cnres@mpGridLineColor       = "gray"
  cnres@mpGridLineThicknessF  = 0.02;0.01 

  cnres@cnConstFEnableFill =True


  ;cnres@lbBoxMinorExtentF     =  0.15                   ;-- decrease height of labelbar

;------------------------------------------------ 
Cases_Label = (/"(a) 1xCO~B1~2 ~NN~"," (b) Poles_25.76MT","(c) Poles_53.72Mt"/)
               
  plot = new(3,graphic)
  cnres@gsnLeftString  = Cases_Label(0)
  cnres@gsnRightString = Label_Global_mean(0)
  plot(0)              = gsn_csm_contour_map(wks,Change_TS(0,:,:),cnres)
  ; zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(0),Change_sig(0,:,:),zres1)
  ; STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  ; overlay(zonal_mean,STD_plot1)
 

  cnres@gsnLeftString  = Cases_Label(1)
  cnres@gsnRightString = Label_Global_mean(1)
  plot(1)              = gsn_csm_contour_map(wks,Change_TS(1,:,:),cnres)
  ; zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(1),Change_sig(1,:,:),zres1)
  ; STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  ; overlay(zonal_mean,STD_plot1)

  cnres@gsnLeftString  = Cases_Label(2)
  cnres@gsnRightString = Label_Global_mean(2)  
  plot(2)              = gsn_csm_contour_map(wks,Change_TS(2,:,:),cnres)
  ; zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(2),Change_sig(2,:,:),zres1)
  ; STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  ; overlay(zonal_mean,STD_plot1)

  
;************************************************
; create panel
;************************************************
  resP                           = True                ; modify the panel plot
  resP@gsnPanelMainString        = ""
  resP@gsnPanelLabelBar          = True                ; add common colorbar
  resP@lbLabelFontHeightF        = 0.007               ; make labels smaller
  resP@lbTitleOn                 = True
  resP@lbLabelStride             = 2
  resP@lbTitleString             = "K" ;"kJ/cm^2"
  resP@lbTitleFontHeightF        = 0.008
  resP@lbTitleFont               = "times-roman"
  resP@lbTitlePosition           = "Bottom"
  resP@pmLabelBarOrthogonalPosF  = -0.1
  resP@pmLabelBarWidthF            = 0.4                                         
  resP@pmLabelBarHeightF           = 0.05 
  ;resP@lbOrientation              = "Vertical"     ; vertical label bar
  resP@gsnPanelFigureStringsFont = "times-roman"
  resP@gsnPanelBottom            = 0.05                 ; add space at bottom
; resP@gsnMaximize               = True                 ; use full page
  resP@amJust   	          = "TopRight"
  resP@pmLabelBarOrthogonalPosF = -.03
  resP@pmLabelBarParallelPosF = .04
  resP@lbLabelFont               = "times-roman"
  resP@lbLabelFontHeightF  = 0.015     ; make labels smaller
  resP@lbTitleFontHeightF   =0.015
  resP@gsnPanelYWhiteSpacePercent = 6.5
  resP@gsnPanelXWhiteSpacePercent = 4.5
;  resP@txString   = "Temperature (~S~o~N~C)(model climatology - year 41 to 100)"
  resP@gsnMaximize      = True
  resP@gsnPaperOrientation = "portrait"


; resP@gsnPanelFigureStrings= (/Label_Global_mean_percent(0), Label_Global_mean_percent(1),Label_Global_mean_percent(2), Label_Global_mean_percent(3), Label_Global_mean_percent(4), Label_Global_mean_percent(5)/) ; add strings to panel
  resP@gsnPanelFigureFontHeightF = -0.1

;----------------------------------------------------------------------
; Draw lines at the lon/lon coordinate array values.
;----------------------------------------------------------------------
  pres                   = True
  ;pres@gsnDraw           = False
  ;pres@gsnFrame          = False
  pres@gsnCoordsAsLines  = False
  pres@gsLineThicknessF  = 2
  pres@gsMarkerColor   = "darkorchid4"
  ;gsn_coordinates(wks,plot,diff_sig,pres)
  ;delete(pres@gsnCoordsAsLines)
;----------------------------------------------------------------------

  gsn_panel(wks,plot,(/3,2/),resP)               ; now draw as one plot
  ;draw(plot)
  ;frame(wks)
  ;exit()

  delete(wks)

  cmd = "convert -geometry 2500x2500 -density 300 -trim " + pname + ".eps " + \
                                                          pname + ".png"
  system(cmd)
  
end
  
  
 