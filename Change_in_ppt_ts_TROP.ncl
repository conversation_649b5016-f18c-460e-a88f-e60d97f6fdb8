load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_code.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/gsn_csm.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/contributed.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/csm/shea_util.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRFUserARW.ncl"
load "/usr/share/ncarg/lib/ncarg/nclscripts/wrf/WRF_contributed.ncl"

begin
;*************************************************
e_path1="/home/<USER>/Documents/newdata_pgms"
e_1CO2=addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/Yearly_E_CO2_01_100new1.nc","r")
e_2CO2 = addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/Yearly_E2000_2CO2_01_100_560_2D_1.nc", "r")
;e_POL = addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/Yearly_E2000_POLES_65_90_01_100_53_1.nc", "r")
e_POL = addfile("/home/<USER>/Documents/my_work_draft/Forcing_Files/Final/Yearly_E2000_65_90_25_76_560_01_100_2D_1.nc", "r")
 lat=e_1CO2->lat({-30:30})
printVarSummary(lat)
PT_1CO2=e_1CO2->PRECT(40:99,{-30:30},:)*8.64e+7
PT_2CO2=e_2CO2->PRECT(40:99,{-30:30},:)*8.64e+7
PT_POL=e_POL->PRECT(40:99,{-30:30},:)*8.64e+7

TS_1CO2=e_1CO2->TS(40:99,{-30:30},:)
TS_2CO2=e_2CO2->TS(40:99,{-30:30},:)
TS_POL=e_POL->TS(40:99,{-30:30},:)

;---------------------------------------------------------
dummy=e_1CO2->PRECC(0:5,{-30:30},:)
dummy@_FillValue =-999.999


Mean=new((/6,32,144/),typeof(dummy),dummy@_FillValue)
Mean(0,:,:)=dim_avg_n_Wrap(TS_2CO2,0)
Mean(1,:,:)=dim_avg_n_Wrap(TS_1CO2,0)
Mean(2,:,:)=dim_avg_n_Wrap(TS_POL,0)
Mean(3,:,:)=dim_avg_n_Wrap(PT_2CO2,0)
Mean(4,:,:)=dim_avg_n_Wrap(PT_1CO2,0)
Mean(5,:,:)=dim_avg_n_Wrap(PT_POL,0)

copy_VarCoords(dummy,Mean)
printVarSummary(Mean)


VAR=new((/6,32,144/),typeof(dummy),dummy@_FillValue)
VAR(0,:,:)=dim_variance_n_Wrap(TS_2CO2,0)
VAR(1,:,:)=dim_variance_n_Wrap(TS_1CO2,0)
VAR(2,:,:)=dim_variance_n_Wrap(TS_POL,0)
VAR(3,:,:)=dim_variance_n_Wrap(PT_2CO2,0)
VAR(4,:,:)=dim_variance_n_Wrap(PT_1CO2,0)
VAR(5,:,:)=dim_variance_n_Wrap(PT_POL,0)

copy_VarCoords(dummy,VAR)
printVarSummary(VAR)

dummy2=e_1CO2->PRECC(0:3,{-30:30},:)
dummy2@_FillValue =-999.999
Change_PT_TS=new((/4,32,144/),typeof(dummy2),dummy2@_FillValue)
Change_PT_TS(0,:,:)=Mean(1,:,:)-Mean(0,:,:)
Change_PT_TS(1,:,:)=Mean(2,:,:)-Mean(0,:,:)
Change_PT_TS(2,:,:)=Mean(4,:,:)-Mean(3,:,:)
Change_PT_TS(3,:,:)=Mean(5,:,:)-Mean(3,:,:)

copy_VarCoords(dummy2,Change_PT_TS)

;---------------------------------------------------------
prob_T_P=new((/4,32,144/),typeof(dummy),dummy@_FillValue)

prob_T_P(0,:,:)=ttest(Mean(0,:,:),VAR(0,:,:),60,Mean(1,:,:),VAR(1,:,:),60,False,False)
prob_T_P(1,:,:)=ttest(Mean(0,:,:),VAR(0,:,:),60,Mean(2,:,:),VAR(2,:,:),60,False,False)
prob_T_P(2,:,:)=ttest(Mean(3,:,:),VAR(3,:,:),60,Mean(4,:,:),VAR(3,:,:),60,False,False)
prob_T_P(3,:,:)=ttest(Mean(3,:,:),VAR(3,:,:),60,Mean(5,:,:),VAR(4,:,:),60,False,False)
copy_VarCoords(dummy2,prob_T_P)

dummy2_1=e_1CO2->PRECC(0:3,{-30:30},:)
dummy2_1@_FillValue =-999.999
alpha=(1-prob_T_P)*100
copy_VarCoords(dummy2_1,alpha)

Change_sig=mask(Change_PT_TS,(alpha.ge.95),True) ;90% 
copy_VarCoords(dummy2_1,Change_sig)


;******************************************                 Areal average
lat=e_1CO2->lat({-30:30})
lon=e_1CO2->lon
jlat  = dimsizes( lat )
  rad    = 4.0*atan(1.0)/180.0
  re     = 6371220.0
  rr     = re*rad
  dlon   = abs(lon(2)-lon(1))*rr
  dx    = dlon*cos(lat*rad)
  dy     = new ( jlat, typeof(dx))
  dy(0)  = abs(lat(2)-lat(1))*rr
  dy(1:jlat-2)  = abs(lat(2:jlat-1)-lat(0:jlat-3))*rr*0.5   
  dy(jlat-1)    = abs(lat(jlat-1)-lat(jlat-2))*rr
  area   = dx*dy
  clat   = cos(lat*rad)
  ;****************************************************
  dummy3=e_1CO2->PRECC(0:3,1,1)
  dummy3@_FillValue =-999.999
  Global_mean_percent = new(4, typeof(Change_PT_TS))  ; Assuming you have 6 data points
Global_mean_percent(0:1)=(wgt_areaave_Wrap(Change_PT_TS(0:1,:,:),area,1.0,1))
Global_mean_percent(2:3)=(wgt_areaave_Wrap(Change_PT_TS(2:3,:,:),area,1.0,0)/wgt_areaave_Wrap(Mean(3,:,:),area,1.0,0))*100.

copy_VarCoords(dummy3,Global_mean_percent)

Label_Global_mean_percent = new(4, string)

; Assign values individually (cannot use array slicing)
Label_Global_mean_percent(0) = "Mean=" + decimalPlaces(Global_mean_percent(0), 2, True)
Label_Global_mean_percent(1) = "Mean=" + decimalPlaces(Global_mean_percent(1), 2, True)
Label_Global_mean_percent(2) = "Mean=" + decimalPlaces(Global_mean_percent(2), 2, True) + "%"
Label_Global_mean_percent(3) = "Mean=" + decimalPlaces(Global_mean_percent(3), 2, True) + "%"



;------------------------------------------------------------------------------------------------------------------------------- 
printVarSummary(Change_sig)
Change_sig&lat@units="degrees_north"
Change_sig&lon@units="degrees_east"

;------------------------------------------------ 
wks  = gsn_open_wks("eps","plot_TS_precipitation_change_POLES_TROP")      ; send graphics to PNG file
;___________________________________________________________________________________________________________________
;----------------------------------------------------------------------
; Set list of map projections. Not including "PseudoMollweide" here.
;----------------------------------------------------------------------
projections = (/"Orthographic","Stereographic","PolarStereographic",\
               "LambertEqualArea","Gnomonic","AzimuthalEquidistant",\
               "Satellite","Mercator","CylindricalEquidistant",\
               "LambertConformal","MaskedLambertConformal",\
               "Robinson","CylindricalEqualArea","RotatedMercator",\
               "Aitoff","Hammer","Mollweide","WinkelTripel"/)
nproj = dimsizes(projections)
;......................................for TS
gsn_define_colormap(wks,"WhiteBlue") ; GMT_relief_oceanonly ViBlGrWhYeOrRe
;gsn_reverse_colormap(wks)

cnres                             = True
cnres@gsnMaximize                 = False
cnres@cnFillPalette = "WhiteBlue"  ; Temperature colors
;cnres@cnFillDrawOrder             = "PreDraw"       ; draw contours before continents
cnres@gsnDraw                     = False
cnres@gsnFrame                    = False
cnres@cnLinesOn                   = False
cnres@cnLineThicknessF            = 0.5
cnres@cnLineLabelsOn              = False
cnres@cnFillOn                    = True

;cnres@mpFillOn                    = False
;cnres@mpGeophysicalLineColor      = "black"
;cnres@mpGeophysicalLineThicknessF = 1
cnres@mpLandFillColor              = "white" ;"cornsilk" ;"black"      ;darkolivegreen ;goldenrod4
;cnres@mpLandFillPattern           = 4
cnres@gsnAddCyclic                 = True

cnres@mpCenterLonF                  =180
;cnres@mpLimitMode                  = "LatLon"
;cnres@mpMinLatF                    = -60
;cnres@mpMaxLatF                    = 60
;cnres@mpMinLonF                    = 0
;cnres@mpMaxLonF                    = 360
cnres@cnLevelSelectionMode         = "ManualLevels"
cnres@cnMinLevelValF               = -3.0
cnres@cnMaxLevelValF               = 0.0
cnres@cnLevelSpacingF              = 0.25
cnres@lbLabelStride                = 1
cnres@gsnRightStringFontHeightF    = -0.033
cnres@gsnLeftStringFontHeightF     = -0.033

cnres@gsnRightString               = ""
cnres@gsnLeftString                = ""
cnres@tiMainString                 = "" ;
cnres@tiMainFont                   = "times-roman"
cnres@lbLabelBarOn                 = False            ; turn off individual cb's
cnres@lbBoxEndCapStyle             = "TriangleBothEnds"
;cnres@lbBoxEndCapStyle            = "TriangleHighEnd"
cnres@pmLabelBarWidthF             = 0.04
cnres@pmLabelBaSSTeightF           = 0.23
cnres@lbOrientation                = "Vertical"     ; vertical label bar

cnres@pmTickMarkDisplayMode = "Always"            ; turn on built-in tickmarks
cnres@lbTitleOn             = True
cnres@lbLabelStride         = 2
cnres@lbTitleString         = "P (mm/day)" ;"kJ/cm^2"
cnres@lbTitleFontHeightF    = 0.022
cnres@lbTitleFont           = "times-roman"

cnres@tmXBLabelFontHeightF    = 0.022    ; Make these labels smaller.
cnres@tmYLLabelFontHeightF    = 0.022    ; Make these labels smaller.


cnres@tiYAxisFont 	     = "times-roman"
cnres@tiXAxisFont          = "times-roman"



 cnres@tmXBMajorLengthF     = 0.01
 cnres@tmYLMajorLengthF     = 0.01


cnres@tmYLLabelFont                  = "times-roman"
cnres@tmXBLabelFont                  = "times-roman"
cnres@tmXBLabelsOn                   = False

cnres@tmYRBorderOn = False
cnres@tmYLBorderOn = False
cnres@tmXBBorderOn = False
cnres@tmXTBorderOn = False

cnres@tmYRLabelsOn = True
cnres@tmXBMajorLengthF=0.03
cnres@tmYRMajorLengthF=0.03
cnres@tmXBOn               = False     ; Turn off top tickmarks
cnres@tmXTOn               = False     ; Turn off top tickmarks
;cnres@tmYLOn               = False     ; Turn off left tickmarks
;cnres@tmYROn               = True      ; Turn off bottom tickmarks

cnres@tmXBMajorOutwardLengthF =-0.02
cnres@tmXBLabelStride =2
cnres@tmYLLabelStride =2


;cnres@gsnLeftString        = ""
;cnres@gsnLeftStringFontHeightF = 0.019


;cnres@gsnZonalMean                = True         ; add zonal plot
;cnres@gsnZonalMeanXMinF           = -1.          ; set minimum X-axis value for zonal mean plot  
;cnres@gsnZonalMeanXMaxF           = 1.           ; set maximum X-axis value for zonal mean plot  
;cnres@gsnZonalMeanYRefLine        = 0.0          ; set reference line X-axis value



;cnres@vpWidthF              =  1.0
;cnres@vpHeightF             =  1.0
;cnres@vpXF                  =  0.05
;cnres@vpYF                  =  0.95

dlon = 30
dlat = 30

;cnres@mpProjection          = "Robinson"
cnres@mpPerimOn             =  True
cnres@mpGridAndLimbOn       =  True
cnres@mpGridLatSpacingF     =  dlat
cnres@mpGridLonSpacingF     =  dlon
cnres@mpGridLineColor       = "gray"
cnres@mpLimitMode  = "LatLon"
cnres@mpMinLatF    = -30
cnres@mpMaxLatF    = 30
cnres@cnConstFEnableFill =True


;cnres@lbBoxMinorExtentF     =  0.15                   ;-- decrease height of labelbar
;----------------------------------------------------------------------
gsn_define_colormap(wks,"MPL_RdBu") ;

cnres1                             = True
cnres1@gsnMaximize                 = False
cnres1@cnFillPalette = "MPL_RdBu"  ; Temperature colors
;cnres@gsnMaximize = False
;cnres@cnFillDrawOrder             = "PreDraw"       ; draw contours before continents
cnres1@gsnDraw                     = False
cnres1@gsnFrame                    = False
cnres1@cnLinesOn                   = False
cnres1@cnLineThicknessF            = 0.5
cnres1@cnLineLabelsOn              = False
cnres1@cnFillOn                    = True
cnres1@mpLimitMode  = "LatLon"
cnres1@mpMinLatF    = -20
cnres1@mpMaxLatF    = 20
;cnres@mpFillOn                    = False
;cnres@mpGeophysicalLineColor      = "black"
;cnres@mpGeophysicalLineThicknessF = 1
cnres1@mpLandFillColor              = "white" ;"cornsilk" ;"black"      ;darkolivegreen ;goldenrod4
;cnres@mpLandFillPattern           = 4
cnres1@gsnAddCyclic                 = True

;cnres@mpProjection                  = "Robinson"
;cnres@mpPerimOn                     = False         ; turn off map perimeter

cnres1@mpCenterLonF                  = 180
;cnres@mpLimitMode                  = "LatLon"
cnres@mpMinLatF                    = -30
cnres@mpMaxLatF                    = 30
cnres@mpMinLonF                    = 0
cnres@mpMaxLonF                    = 360
cnres1@cnLevelSelectionMode         = "ManualLevels"
cnres1@cnMinLevelValF               = -2.0
cnres1@cnMaxLevelValF               = 2.0
cnres1@cnLevelSpacingF              = 0.25
cnres1@lbLabelStride                = 4
cnres1@gsnRightStringFontHeightF    = -0.033
cnres1@gsnLeftStringFontHeightF     = -0.033

cnres1@gsnRightString               = ""
cnres1@gsnLeftString                = ""
cnres1@tiMainString                 = "" ;
cnres1@tiMainFont                   = "times-roman"
cnres1@lbLabelBarOn                 = False            ; turn off individual cb's
cnres1@lbBoxEndCapStyle             = "TriangleBothEnds"
;cnres@lbBoxEndCapStyle            = "TriangleHighEnd"
cnres1@pmLabelBarWidthF             = 0.04
cnres1@pmLabelBaSSTeightF           = 0.23
cnres1@lbOrientation                = "Vertical"     ; vertical label bar

cnres1@pmTickMarkDisplayMode = "Always"            ; turn on built-in tickmarks
cnres1@lbTitleOn             = True
cnres1@lbLabelStride         = 2
cnres1@lbTitleString         = "P (mm/day)" ;"kJ/cm^2"
cnres1@lbTitleFontHeightF    = 0.022
cnres1@lbTitleFont           = "times-roman"

cnres1@tmXBLabelFontHeightF    = 0.022    ; Make these labels smaller.
cnres1@tmYLLabelFontHeightF    = 0.022    ; Make these labels smaller.


cnres1@tiYAxisFont 	     = "times-roman"
cnres1@tiXAxisFont          = "times-roman"



 cnres1@tmXBMajorLengthF     = 0.01
 cnres1@tmYLMajorLengthF     = 0.01


cnres1@tmYLLabelFont                  = "times-roman"
cnres1@tmXBLabelFont                  = "times-roman"
cnres1@tmXBLabelsOn                   = False

cnres1@tmYRBorderOn = False
cnres1@tmYLBorderOn = False
cnres1@tmXBBorderOn = False
cnres1@tmXTBorderOn = False

cnres1@tmYRLabelsOn = True
cnres1@tmXBMajorLengthF=0.03
cnres1@tmYRMajorLengthF=0.03
cnres1@tmXBOn               = False     ; Turn off top tickmarks
cnres1@tmXTOn               = False     ; Turn off top tickmarks
;cnres@tmYLOn               = False     ; Turn off left tickmarks
;cnres@tmYROn               = True      ; Turn off bottom tickmarks

cnres1@tmXBMajorOutwardLengthF =-0.02
cnres1@tmXBLabelStride =2
cnres1@tmYLLabelStride =2


;cnres@gsnLeftString        = ""
;cnres@gsnLeftStringFontHeightF = 0.019


;cnres@gsnZonalMean                = True         ; add zonal plot
;cnres@gsnZonalMeanXMinF           = -1.          ; set minimum X-axis value for zonal mean plot  
;cnres@gsnZonalMeanXMaxF           = 1.           ; set maximum X-axis value for zonal mean plot  
;cnres@gsnZonalMeanYRefLine        = 0.0          ; set reference line X-axis value



;cnres@vpWidthF              =  1.0
;cnres@vpHeightF             =  1.0
;cnres@vpXF                  =  0.05
;cnres@vpYF                  =  0.95

dlon = 30
dlat = 30

;cnres1@mpProjection          = "Robinson"
cnres1@mpPerimOn             =  True
cnres1@mpGridAndLimbOn       =  True
cnres1@mpGridLatSpacingF     =  dlat
cnres1@mpGridLonSpacingF     =  dlon
cnres1@mpGridLineColor       = "gray"


;cnres@lbBoxMinorExtentF     =  0.15                   ;-- decrease height of labelbar

;------------------------------------------------ 
zres              = True
zres@gsnDraw                     = False
zres@gsnFrame                    = False

;zres@gsnMaximize = False

zres@tmXBMode     = "Explicit"                ; Define own tick mark labels.
zres@tmXBValues   = (/-1.5,0,1.5/)
zres@tmXBLabels   = zres@tmXBValues + ""   ;
zres@tmXBLabelFontColor   = "dodgerblue1" 


zres@trXMinF                 = -2.      ; Could also use gsnZonalMeanXMinF
zres@trXMaxF                 = 2.      ; Could also use gsnZonalMeanXMaxF

zres@xyLineThicknessF        = 3.5
zres@xyLineColor             =  "dodgerblue1" ;mediumslateblue dodgerblue1
zres@gsnXRefLine             = 0
zres@gsnZonalMeanYRefLine    = 10
zres@gsnYRefLineThicknessesF = 0.05
zres@gsnYRefLineDashPatterns = 1
zres@gsnYRefLineColor        = "grey53"


zres@tiMainString              = "" 
zres@gsnRightString            = ""
zres@gsnRightStringFont        = "times-roman"
zres@gsnRightStringFontHeightF = 0.022




zres@tmXTOn                   = False      ; Turn off top tickmarks
zres@tmYLOn                   = False     ; Turn off bottom tickmarks
zres@tmYROn                   = False      ; Turn off bottom tickmarks
;zres@tmXBLabelAngleF          =45
zres@tmYRLabelsOn = False


zres@tmXBLabelFontThicknessF = 1.5
zres@tmXBLabelFontHeightF    = 0.030    ; Make these labels smaller.
;zres@tmYRLabelFontHeightF    = 0.053    ; Make these labels smaller.
zres@tmXBLabelFont           = "times-roman"
zres@tmYRLabelFont           = "times-roman"

;zres@tmYRMinorOn = False

zres@tiYAxisFont = "times-roman"
zres@tiYAxisString = "Latitude"
zres@tiYAxisSide = "Right"

;zres@tiYAxisFontHeightF = 0.033

 ;zres@tmXBMajorLengthF     = 0.02
 ;zres@tmYLMajorLengthF     = 0.008
 ;zres@tmYRMinorLengthF     = 0.00

;zres@tmYRMajorOutwardLengthF =-0.01
;zres@tmYRLabelStride =2









zres@vpWidthF               =  0.2
;zres@vpHeightF             =  0.46
;zres@vpXF                  =  0.05
;zres@vpYF                  =  0.95

zres2 = zres
;zres2@gsnXYFillColors = "LightBlue"
;zres2@xyLineColor     = -1
zres2@xyLineColor             =  "dodgerblue1" ;
zres2@gsnYRefLineThicknessesF = 0.1 
; Create a plot with the area between both curves filled in pink.
delete(zres2@xyLineColors)
zres2@gsnXYFillColors = "deepskyblue"
zres2@gsFillOpacityF  = 0.1
zres2@xyLineColor     = -1                           ; We don't want the line, so make it transparent.


zres3=zres2
zres3@gsnXYFillColors = "grey57"
zres3@xyLineColor     = -1

;--------------------------
;------------------------------------------------ 
Cases_Label = (/"(a) 1xCO~B1~2 ~NN~"," (b) POLES","(c) 1xCO~B1~2 ~NN~","(d) POLES"/)
               
  plot = new(4,graphic)
  cnres@gsnLeftString  = Cases_Label(0)
  cnres@gsnRightString = Label_Global_mean_percent(0)
  plot(0)              = gsn_csm_contour_map(wks,Change_PT_TS(0,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(0),Change_PT_TS(0,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(0:1,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  ;STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot1)

  ;zonal_plot = gsn_csm_zonal_means(wks,Change_P(0,:,:),zres2)
  ;zonal_plot_R  = gsn_csm_xy (wks,R_STD(0,:),lat,zres2)   ; create plot. 
  ;zonal_plot_L  = gsn_csm_xy (wks,L_STD(0,:),lat,zres2)   ; create plot. 
  ;overlay(zonal_mean,zonal_plot_R)
  ;overlay(zonal,zonal_plot_L)
  ;zonal          = gsn_csm_attach_zonal_means(wks,plot(0),Change_P(0,:,:),zres)
 

  
  ;zonal_mean           = gsn_csm_attach_zonal_means(wks,plot(0),Change_P(0,:,:),zres2)
 
  cnres@gsnLeftString  = Cases_Label(1)
  cnres@gsnRightString = Label_Global_mean_percent(1)
  plot(1)              = gsn_csm_contour_map(wks,Change_PT_TS(1,:,:),cnres)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(1),Change_PT_TS(1,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(2:3,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  ;STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
 ; overlay(zonal,STD_plot1)

  cnres1@gsnLeftString  = Cases_Label(2)
  cnres1@gsnRightString = Label_Global_mean_percent(2)  
  plot(2)              = gsn_csm_contour_map(wks,Change_PT_TS(2,:,:),cnres1)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(2),Change_PT_TS(2,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(4:5,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
 ; STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot1)
 
  cnres1@gsnLeftString  = Cases_Label(3) 
  cnres1@gsnRightString = Label_Global_mean_percent(3)
  plot(3)              = gsn_csm_contour_map(wks,Change_PT_TS(3,:,:),cnres1)
  zonal                = gsn_csm_attach_zonal_means(wks,plot(3),Change_PT_TS(3,:,:),zres)
  ;STD_plot  = gsn_csm_xy(wks,SET(6:7,:), R_STD&lat,zres2)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot)
  ;STD_plot1  = gsn_csm_xy(wks,SET1(0:1,:), R_STD&lat,zres3)  ; Create another filled XY plot.
  ;overlay(zonal,STD_plot1)

;------------------------------
; plot statistical significance
;------------------------------

sgres                      = True		; significance
sgres@gsnDraw              = False		; draw plot
sgres@gsnFrame             = False		; advance frome
sgres@cnInfoLabelOn        = False		; turn off info label
sgres@cnLinesOn            = False		; draw contour lines
sgres@cnLineLabelsOn       = False		; draw contour labels
sgres@cnFillScaleF         = 0.7		; add extra density
sgres@cnFillDotSizeF       = 0.002

sgres@gsnAddCyclic         = True

; activate if gray shading for B&W plot  
sgres@cnFillOn             = True
sgres@cnFillColors         = (/"transparent","transparent"/) ; choose one color for our single cn level
sgres@cnLevelSelectionMode = "ExplicitLevels"	         ; set explicit contour levels
sgres@cnLevels             = 95.0                            ; only set one level
sgres@lbLabelBarOn         = False

sgres@tiMainString         = ""     ; title
sgres@gsnCenterString      = ""  ; subtitle
sgres@gsnLeftString        = ""    ; upper-left subtitle
sgres@gsnRightString       = ""   ; upper-right subtitle

sig_plot0 = gsn_csm_contour(wks,alpha(0,:,:),sgres)
sig_plot1 = gsn_csm_contour(wks,alpha(1,:,:),sgres)
sig_plot2 = gsn_csm_contour(wks,alpha(2,:,:),sgres)
sig_plot3 = gsn_csm_contour(wks,alpha(3,:,:),sgres)


opt                  = True
opt@gsnShadeFillType = "pattern"
;opt@gsnShadeHigh     = 17
opt@gsnShadeLow      = 17

sig_plot0 = gsn_contour_shade(sig_plot0,95.0,-999,opt)
sig_plot1 = gsn_contour_shade(sig_plot1,95.0,-999,opt)
sig_plot2 = gsn_contour_shade(sig_plot2,95.0,-999,opt)
sig_plot3 = gsn_contour_shade(sig_plot3,95.0,-999,opt)


;overlay(plot(0),sig_plot0)
;overlay(plot(1),sig_plot1)
overlay(plot(2),sig_plot2)
overlay(plot(3),sig_plot3)








;************************************************
; create panel
;************************************************
; Define the panel resources for Temperature
resP_Temp                           = True
resP_Temp@gsnPanelMainString        = ""
resP_Temp@gsnPanelLabelBar          = True
resP_Temp@lbLabelFontHeightF        = 0.015
resP_Temp@lbLabelFont               = "times-roman"
resP_Temp@lbTitleOn                 = True
resP_Temp@lbLabelStride             = 4
resP_Temp@lbTitleString             = "K"  ; Temperature unit
resP_Temp@lbTitleFontHeightF        = 0.015
resP_Temp@lbTitleFont               = "times-roman"
resP_Temp@lbTitlePosition           = "Bottom"
resP_Temp@pmLabelBarOrthogonalPosF  = -0.1
resP_Temp@pmLabelBarParallelPosF    = 0.04
resP_Temp@pmLabelBarWidthF          = 0.6
resP_Temp@pmLabelBarHeightF         = 0.08
resP_Temp@gsnPanelYWhiteSpacePercent = 2.5
resP_Temp@gsnPanelXWhiteSpacePercent = 4.5
resP_Temp@gsnMaximize               = True
resP_Temp@gsnPaperOrientation       = "portrait"

; Define the panel resources for Precipitation
resP_Precip                           = True
resP_Precip@gsnPanelMainString        = ""
resP_Precip@gsnPanelLabelBar          = True
resP_Precip@lbLabelFontHeightF        = 0.015
resP_Precip@lbLabelFont               = "times-roman"
resP_Precip@lbTitleOn                 = True
resP_Precip@lbLabelStride             = 4
resP_Precip@lbTitleString             = "mm ~NN~ day~S~-1"  ; Precipitation unit
resP_Precip@lbTitleFontHeightF        = 0.015
resP_Precip@lbTitleFont               = "times-roman"
resP_Precip@lbTitlePosition           = "Bottom"
resP_Precip@pmLabelBarOrthogonalPosF  = -0.03
resP_Precip@pmLabelBarParallelPosF    = 0.04
resP_Precip@pmLabelBarWidthF          = 0.6
resP_Precip@pmLabelBarHeightF         = 0.08
resP_Precip@gsnPanelYWhiteSpacePercent = 2.5
resP_Precip@gsnPanelXWhiteSpacePercent = 4.5
resP_Precip@gsnMaximize               = True
resP_Precip@gsnPaperOrientation       = "portrait"

; Panel the first two plots (Temperature)
gsn_panel(wks, plot(0:1), (/2,2/), resP_Temp)

; Panel the last two plots (Precipitation)
gsn_panel(wks, plot(2:3), (/2,2/), resP_Precip)
exit()

end


